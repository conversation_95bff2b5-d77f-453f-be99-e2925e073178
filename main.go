package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode"

	"github.com/PuerkitoBio/goquery"
	"golang.org/x/net/proxy"
)

func printStartupBanner(name string) {
	fmt.Println("# " + name)
	fmt.Println("Version: 0.0.1")
	fmt.Println("Build: " + buildDate)
	fmt.Println("Commit: " + commitHash)
	fmt.Println("Author: https://t.me/suenot")
	fmt.Println("---")
}

var buildDate = "unknown"
var commitHash = "unknown"

type apiResponse struct {
	Success bool    `json:"success"`
	Data    apiData `json:"data"`
}

type apiData struct {
	TotalPages int      `json:"total_pages"`
	TotalCount int      `json:"total_count"`
	Notices    []notice `json:"notices"`
}

type notice struct {
	ListedAt      string `json:"listed_at"`
	FirstListedAt string `json:"first_listed_at"`
	ID            int    `json:"id"`
	Title         string `json:"title"`
	Category      string `json:"category"`
}

type cacheItem struct {
	DateUTC                    string    `json:"date_utc,omitempty"`
	DateMSK                    string    `json:"date_msk,omitempty"`
	DateKST                    string    `json:"date_kst,omitempty"`
	DateLegacy                 string    `json:"date,omitempty"`
	Category                   string    `json:"category,omitempty"`
	ListedAt                   string    `json:"listed_at,omitempty"`
	FirstListedAt              string    `json:"first_listed_at,omitempty"`
	ListedAtTimestamp          int64     `json:"listed_at_timestamp,omitempty"`
	FirstListedAtTimestamp     int64     `json:"first_listed_at_timestamp,omitempty"`
	ID                         int       `json:"id,omitempty"`
	Href                       string    `json:"href"`
	Symbol                     string    `json:"symbol"`
	Title                      string    `json:"title"`
	Listing                    bool      `json:"listing"`
	SavedAtTimestamp           int64     `json:"saved_at_timestamp,omitempty"`
	Timestamp                  int64     `json:"-"`
	Exchanges                  []string  `json:"exchanges,omitempty"`
	MinuteReturnsListedAt      []float64 `json:"minute_returns_listed_at,omitempty"`
	MinuteReturnsFirstListedAt []float64 `json:"minute_returns_first_listed_at,omitempty"`
}

// removed: fetchState (unused)

// listingDocument is the new on-disk format that embeds items and state together
type listingDocument struct {
	Items          []cacheItem `json:"items"`
	LatestID       int         `json:"latest_id,omitempty"`
	LatestHref     string      `json:"latest_href,omitempty"`
	LatestListedAt string      `json:"latest_listed_at,omitempty"`
	SavedAt        string      `json:"saved_at,omitempty"`
}

const (
	outputFile = "listing.json"
)

var (
	maxRetryAfter     = 100 * time.Second
	maxAttempts       = 8
	basePageDelay     = 1000 * time.Millisecond
	pageDelayJitter   = 100 * time.Millisecond
	cycleInterval     = 1000 * time.Millisecond
	pairsDiffInterval = 500 * time.Millisecond
)

// test harness: when UPBIT_TEST_TRADING=1, we don't hit real exchanges; instead we record events here
var testTradeEvents []string
var testEventsMu sync.Mutex

func recordTestEvent(ev string) {
	testEventsMu.Lock()
	defer testEventsMu.Unlock()
	testTradeEvents = append(testTradeEvents, ev)
}

// Trading configuration (loaded from config)
var (
	tradeEnabled    bool
	orderDirection  string
	orderAmountUSDT float64
	orderLeverage   int
	orderTPPercents []float64
	orderSLPercents []float64
	tradeOnBybit    bool
	tradeOnBingx    bool
)

// loaded from config file if present
var configuredAccounts []AccountConfig
var loadedConfig *Config
var currentConfig *Config // Global config for URL access

// ========================= Trailing Stop Management =========================

type trailingStopPosition struct {
	Symbol          string
	Exchange        string
	Direction       string
	EntryPrice      float64
	CurrentSL       float64
	TrailingPercent float64
	HighestPrice    float64 // for long positions
	LowestPrice     float64 // for short positions
	Quantity        float64
	Category        string
	APIKey          string
	APISecret       string
	AccountName     string
	BingxSymbol     string // for BingX format (e.g., "API3-USDT")
	PositionSide    string // for BingX ("LONG" or "SHORT")
}

var activeTrailingStops []trailingStopPosition
var trailingStopsMu sync.RWMutex

// ========================= Telegram helpers =========================

type telegramSendMessageRequest struct {
	ChatID                int64  `json:"chat_id"`
	Text                  string `json:"text"`
	DisableWebPagePreview bool   `json:"disable_web_page_preview"`
}

func sendTelegramMessage(ctx context.Context, botToken string, chatID int64, text string) error {
	if strings.TrimSpace(botToken) == "" || chatID == 0 || strings.TrimSpace(text) == "" {
		return nil
	}
	apiURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", botToken)
	body, _ := json.Marshal(telegramSendMessageRequest{ChatID: chatID, Text: text, DisableWebPagePreview: true})
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, strings.NewReader(string(body)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		data, _ := io.ReadAll(resp.Body)
		log.Printf("telegram: sendMessage failed status=%d body=%s", resp.StatusCode, string(data))
		return fmt.Errorf("telegram send failed: %s", resp.Status)
	}
	return nil
}

func formatSignalMessage(it cacheItem) string {
	ts := time.UnixMilli(it.Timestamp).Format("2006-01-02 15:04:05 MST")
	sym := strings.ToUpper(strings.TrimSpace(it.Symbol))
	ex := ""
	if len(it.Exchanges) > 0 {
		ex = " | ex: " + strings.Join(it.Exchanges, ",")
	}
	return fmt.Sprintf("Signal: %s listing detected\nTitle: %s\nTime: %s%s", sym, it.Title, ts, ex)
}

func spawnTelegramSignals(items []cacheItem) {
	if loadedConfig == nil || !loadedConfig.TelegramSignals.Enabled {
		return
	}
	bot := strings.TrimSpace(loadedConfig.TelegramSignals.BotToken)
	chat := loadedConfig.TelegramSignals.ChatID
	if bot == "" || chat == 0 {
		return
	}
	for _, it := range items {
		if !it.Listing {
			continue
		}
		itCopy := it
		go func() {
			time.Sleep(1 * time.Second)
			msg := formatSignalMessage(itCopy)
			if err := sendTelegramMessage(context.Background(), bot, chat, msg); err != nil {
				log.Printf("telegram: signal send failed: %v", err)
			}
		}()
	}
}

// HTTP clients pool: direct + proxies from config
var httpClients []*http.Client
var httpClientLabels []string
var proxyCyclesStarted bool // Track if proxy cycles are already running

// Proxy statistics tracking
type proxyStats struct {
	successCount      int
	errorCount        int
	lastSuccess       time.Time
	lastError         time.Time
	consecutiveErrors int
	enabled           bool
	lastUpdateTime    time.Time // Time of last successful data update
}

// Interval tuning statistics
type intervalStats struct {
	startTime        time.Time
	errorCount       int
	successCount     int
	lastTuneTime     time.Time
	rollbackCount    int
	originalInterval int
	currentInterval  int
	isFixed          bool
}

var newsIntervalStats *intervalStats
var pairsDiffIntervalStats *intervalStats
var serviceCenterIntervalStats *intervalStats
var intervalStatsMutex sync.RWMutex

// Service center HTML parsing regexes
var (
	phraseRegex = regexp.MustCompile(`(?i)(?:Trade\s+)?Market\s*Support\s*for\s+[^()\n]*\(\s*([A-Z0-9-]{2,20})\s*\)`)
	symbolRegex = regexp.MustCompile(`\(\s*([A-Z0-9-]{2,20})\s*\)`)
	dateRegexes = []*regexp.Regexp{
		regexp.MustCompile(`\b\d{4}[./-]\d{1,2}[./-]\d{1,2}(?:\s+\d{1,2}:\d{2})?\b`),
		regexp.MustCompile(`(?i)\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Sept|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}(?:\s+\d{1,2}:\d{2})?\b`),
		regexp.MustCompile(`\b\d{4}\s*년\s*\d{1,2}\s*월\s*\d{1,2}\s*일(?:\s*\d{1,2}:\d{2})?\b`),
	}
)

var proxyStatsMap map[string]*proxyStats
var proxyStatsMutex sync.RWMutex

// Configuration for proxy management
const (
	maxConsecutiveErrors = 3   // Disable proxy after 3 consecutive errors
	errorThreshold       = 0.7 // Disable if error rate > 70%
	minSuccessRate       = 0.3 // Minimum success rate to keep proxy enabled
	statsWindowSize      = 10  // Track last 10 attempts
)

// Configuration for interval tuning (loaded from config)
var (
	intervalTuneStep      int           // Adjust interval by X ms
	intervalCheckPeriod   time.Duration // Check period
	maxRollbackCount      int           // Fix interval after X rollbacks
	minInterval           int           // Minimum interval
	maxInterval           int           // Maximum interval
	intervalTuningEnabled bool          // Whether tuning is enabled
)

// buildHTTPClients constructs a direct client and, if provided, one client per
// proxy from config in the format scheme:host:port:user:pass where scheme is http or socks5.
// Clients share sane timeouts. Invalid proxy entries are skipped with a warning.
func buildHTTPClients(cfg *Config) {
	httpClients = nil
	httpClientLabels = nil

	// Initialize proxy statistics
	proxyStatsMap = make(map[string]*proxyStats)
	proxyStatsMutex.Lock()
	defer proxyStatsMutex.Unlock()

	// Always include direct client first
	httpClients = append(httpClients, &http.Client{Timeout: 20 * time.Second})
	httpClientLabels = append(httpClientLabels, "direct")

	// Initialize stats for direct client
	proxyStatsMap["direct"] = &proxyStats{
		enabled:        true,
		lastSuccess:    time.Now(),
		lastUpdateTime: time.Now(),
	}

	// Check if we're in dev mode - disable proxies for dev
	useProxies := true
	if cfg != nil && cfg.Settings.API.Playground == "dev" {
		useProxies = false
		log.Printf("info: dev mode detected, proxies disabled (localhost doesn't need proxies)")
	} else if cfg != nil && !cfg.Settings.AnnouncementsAPI.Proxy && !cfg.Settings.MarketsAPI.Proxy {
		useProxies = false
		log.Printf("info: proxies disabled in both announcements_api and markets_api settings")
	} else if cfg != nil && !cfg.Settings.AnnouncementsAPI.Proxy {
		log.Printf("info: proxies disabled in announcements_api settings, but enabled for markets_api")
	} else if cfg != nil && !cfg.Settings.MarketsAPI.Proxy {
		log.Printf("info: proxies disabled in markets_api settings, but enabled for announcements_api")
	}

	// If proxies are disabled in config explicitly, only keep direct client
	if cfg != nil && cfg.ProxiesEnabled != nil && !*cfg.ProxiesEnabled {
		useProxies = false
		log.Printf("info: proxies explicitly disabled in config")
	}

	if !useProxies || cfg == nil || len(cfg.Proxies) == 0 {
		if !useProxies {
			log.Printf("info: proxies not enabled, using only direct connection")
		} else if cfg == nil {
			log.Printf("info: no config available for proxies")
		} else {
			log.Printf("info: no proxy entries found in config")
		}
		return
	}

	log.Printf("info: building HTTP clients with %d proxies", len(cfg.Proxies))

	for _, raw := range cfg.Proxies {
		parts := strings.Split(raw, ":")
		var scheme, host, port, userRaw, passRaw string
		switch len(parts) {
		case 4:
			// legacy: ip:port:user:pass (assume http)
			scheme = "http"
			host = strings.TrimSpace(parts[0])
			port = strings.TrimSpace(parts[1])
			userRaw = strings.TrimSpace(parts[2])
			passRaw = strings.TrimSpace(parts[3])
		case 5:
			// new: scheme:ip:port:user:pass
			scheme = strings.ToLower(strings.TrimSpace(parts[0]))
			host = strings.TrimSpace(parts[1])
			port = strings.TrimSpace(parts[2])
			userRaw = strings.TrimSpace(parts[3])
			passRaw = strings.TrimSpace(parts[4])
		default:
			log.Printf("warn: invalid proxy entry (want scheme:host:port:user:pass where scheme is http or socks5): %s", raw)
			continue
		}
		if scheme != "http" && scheme != "socks5" {
			log.Printf("warn: unsupported proxy scheme '%s' (only http and socks5 supported): %s", scheme, raw)
			continue
		}

		var client *http.Client
		if scheme == "http" {
			user := url.QueryEscape(userRaw)
			pass := url.QueryEscape(passRaw)
			proxyURLStr := fmt.Sprintf("%s://%s:%s@%s:%s", scheme, user, pass, host, port)
			proxyURL, err := url.Parse(proxyURLStr)
			if err != nil {
				log.Printf("warn: bad proxy url %s: %v", proxyURLStr, err)
				continue
			}
			transport := &http.Transport{Proxy: http.ProxyURL(proxyURL)}
			client = &http.Client{Timeout: 20 * time.Second, Transport: transport}
		} else if scheme == "socks5" {
			// Create SOCKS5 dialer with authentication
			dialer, err := proxy.SOCKS5("tcp", fmt.Sprintf("%s:%s", host, port), &proxy.Auth{
				User:     userRaw,
				Password: passRaw,
			}, proxy.Direct)
			if err != nil {
				log.Printf("warn: failed to create SOCKS5 dialer for %s:%s: %v", host, port, err)
				continue
			}

			// Create transport with SOCKS5 dialer
			transport := &http.Transport{
				DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
					return dialer.Dial(network, addr)
				},
			}
			client = &http.Client{Timeout: 20 * time.Second, Transport: transport}
		}
		httpClients = append(httpClients, client)
		clientLabel := scheme + ":" + host + ":" + port
		httpClientLabels = append(httpClientLabels, clientLabel)

		// Initialize stats for this proxy
		proxyStatsMap[clientLabel] = &proxyStats{
			enabled:        true,
			lastSuccess:    time.Now(),
			lastUpdateTime: time.Now(),
		}

		log.Printf("info: added proxy client: %s://%s:%s", scheme, host, port)
	}

	log.Printf("info: total HTTP clients created: %d (1 direct + %d proxies)", len(httpClients), len(httpClients)-1)
}

func main() {
	// Check for test mode
	if len(os.Args) > 1 && os.Args[1] == "test-trading" {
		testTrading()
		return
	}

	// Resolve absolute path for output file
	cwd, _ := os.Getwd()
	outPath := filepath.Join(cwd, outputFile)

	// Seed RNG for jitter
	// rand.Seed is deprecated since Go 1.20, using modern approach
	_ = rand.Int63() // Initialize random generator

	// Load .env first (to populate env vars), then allow tuning via env vars
	loadDotEnv()
	overrideConfigFromEnv()

	printStartupBanner("upbit-listing")

	// Load configuration
	var configPath string
	cfg, err := LoadConfig(configPath)
	if err != nil {
		log.Printf("error: failed to load config: %v", err)
		os.Exit(1)
	}
	if cfg == nil {
		log.Printf("error: no config file found")
		os.Exit(1)
	}
	loadedConfig = cfg
	currentConfig = cfg
	configuredAccounts = cfg.Accounts

	// Apply settings from config
	cycle := cfg.Settings.Cycle
	cycleInterval = time.Duration(cfg.Settings.AnnouncementsAPI.CycleIntervalMs) * time.Millisecond
	if cfg.Settings.MarketsAPI.IntervalMs > 0 {
		pairsDiffInterval = time.Duration(cfg.Settings.MarketsAPI.IntervalMs) * time.Millisecond
	}
	tradeEnabled = cfg.Settings.Trade

	// Apply announcements API settings
	if cfg.Settings.AnnouncementsAPI.MaxAttempts > 0 {
		maxAttempts = cfg.Settings.AnnouncementsAPI.MaxAttempts
	}
	if cfg.Settings.AnnouncementsAPI.MaxRetryAfterSec > 0 {
		maxRetryAfter = time.Duration(cfg.Settings.AnnouncementsAPI.MaxRetryAfterSec) * time.Second
	}
	if cfg.Settings.AnnouncementsAPI.PageDelayMs > 0 {
		basePageDelay = time.Duration(cfg.Settings.AnnouncementsAPI.PageDelayMs) * time.Millisecond
	}
	if cfg.Settings.AnnouncementsAPI.PageDelayJitterMs >= 0 {
		pageDelayJitter = time.Duration(cfg.Settings.AnnouncementsAPI.PageDelayJitterMs) * time.Millisecond
	}

	// Build HTTP clients pool (direct + proxies)
	buildHTTPClients(cfg)

	// Initialize interval tuning statistics
	initIntervalStats(cfg)

	// Build sources from config based on enabled settings
	var sourcesOrder []string
	{
		// Add announcements API if enabled
		if cfg.Settings.AnnouncementsAPI.Enabled {
			sourcesOrder = append(sourcesOrder, "news")
		}
		// Add markets API if enabled
		if cfg.Settings.MarketsAPI.Enabled {
			sourcesOrder = append(sourcesOrder, "pairs_diff")
		}
		// Add service center if enabled
		if cfg.Settings.ServiceCenter.Enabled {
			sourcesOrder = append(sourcesOrder, "service_center")
		}
		// If no sources enabled, default to announcements API
		if len(sourcesOrder) == 0 {
			sourcesOrder = []string{"news"}
		}
	}
	doPairsDiff := false
	for _, s := range sourcesOrder {
		if s == "pairs_diff" {
			doPairsDiff = true
		}
	}

	// Parse exchanges selection from accounts
	tradeOnBybit, tradeOnBingx = false, false
	for _, acc := range configuredAccounts {
		if !acc.Enabled {
			continue
		}
		switch strings.ToLower(acc.Exchange) {
		case "bybit":
			tradeOnBybit = true
		case "bingx":
			tradeOnBingx = true
		}
	}

	if tradeEnabled {
		if len(configuredAccounts) > 0 {
			if err := validateAccountsConfig(configuredAccounts, orderDirection, orderAmountUSDT, orderLeverage); err != nil {
				fmt.Fprintf(os.Stderr, "trade accounts error: %v\n", err)
				os.Exit(2)
			}
			// Start trailing stop monitor if any account has trailing enabled
			hasTrailing := false
			for _, acc := range configuredAccounts {
				if acc.TrailingEnabled && acc.TrailingPercent > 0 {
					hasTrailing = true
					break
				}
			}
			if hasTrailing {
				startTrailingStopMonitor()
				log.Printf("trailing: monitoring started for accounts with trailing stop enabled")
			}
		} else {
			fmt.Fprintf(os.Stderr, "trade enabled but no accounts configured\n")
			os.Exit(2)
		}
	}

	// Prepare pairs_diff mode state if requested
	var (
		apiOut   string
		apiCache string
		client   *http.Client
		known    []string
	)
	if doPairsDiff {
		apiOut = filepath.Join(cwd, "listing_api.json")
		apiCache = filepath.Join(cwd, "upbit_markets_cache.json")
		timeout := time.Duration(cfg.Settings.MarketsAPI.TimeoutSec) * time.Second
		client = &http.Client{Timeout: timeout}

		known = loadMarketsCache(apiCache)
		if len(known) == 0 {
			markets, err := fetchUpbitMarkets(client)
			if err != nil {
				fmt.Fprintf(os.Stderr, "initial fetch failed: %v\n", err)
				os.Exit(1)
			}
			appendNewPairs(apiOut, markets)
			writeMarketsCache(apiCache, markets)
			known = markets
			log.Printf("initialized markets cache with %d entries", len(known))
		} else {
			if _, err := os.Stat(apiOut); err != nil {
				if os.IsNotExist(err) {
					if markets, ferr := fetchUpbitMarkets(client); ferr == nil && len(markets) > 0 {
						appendNewPairs(apiOut, markets)
						log.Printf("seeded listing_api.json with %d markets", len(known))
					}
				}
			}
		}
	}

	// Non-cycle: run each source once in the specified order
	if !cycle {
		for _, s := range sourcesOrder {
			if s == "news" {
				// For non-cycle mode, start client cycles but they will run independently
				if err := startClientCycles(outPath); err != nil {
					fmt.Fprintf(os.Stderr, "error: %v\n", err)
					os.Exit(1)
				}
				// Wait a bit for first iteration to complete
				time.Sleep(2 * time.Second)
			}
			if s == "pairs_diff" {
				markets, err := fetchUpbitMarkets(client)
				if err != nil {
					fmt.Fprintf(os.Stderr, "fetch error: %v\n", err)
					os.Exit(1)
				}
				newOnes := diffNewMarkets(known, markets)
				if len(newOnes) > 0 {
					log.Printf("detected %d new markets", len(newOnes))
					appendNewPairs(apiOut, markets)
					writeMarketsCache(apiCache, markets)
				}
			}
			if s == "service_center" {
				// Run service center parsing once
				if err := runServiceCenterIteration(outPath, 0, "direct"); err != nil {
					log.Printf("service_center error: %v", err)
				}
			}
		}
		return
	}

	// Cycle: perform all requested sources with their own intervals
	for {
		// Start announcements API source if enabled (runs independently)
		if cfg.Settings.AnnouncementsAPI.Enabled {
			if err := startClientCycles(outPath); err != nil {
				log.Printf("cycle: failed to start client cycles: %v", err)
			}
			log.Printf("cycle: announcements API client cycles started, running independently")
		}

		// Start markets API source if enabled (runs independently)
		if cfg.Settings.MarketsAPI.Enabled {
			if cfg.Settings.MarketsAPI.Proxy {
				// Use proxy-enabled cycles similar to news
				if err := startPairsDiffClientCycles(apiOut, apiCache, &known); err != nil {
					log.Printf("cycle: failed to start pairs_diff client cycles: %v", err)
				}
				log.Printf("cycle: pairs_diff client cycles started, running independently")
			} else {
				// Use single client (legacy mode)
				go func() {
					log.Printf("cycle: starting pairs_diff cycle with interval %v", pairsDiffInterval)
					ticker := time.NewTicker(pairsDiffInterval)
					defer ticker.Stop()

					// First iteration immediately
					log.Printf("cycle: pairs_diff initial fetch starting...")
					startTime := time.Now()
					markets, err := fetchUpbitMarkets(client)
					duration := time.Since(startTime)
					if err != nil {
						log.Printf("cycle: pairs_diff initial fetch error after %v: %v", duration, err)
					} else {
						log.Printf("cycle: pairs_diff initial fetch successful after %v, got %d markets", duration, len(markets))
						newOnes := diffNewMarkets(known, markets)
						if len(newOnes) > 0 {
							log.Printf("cycle: pairs_diff detected %d new markets", len(newOnes))
							appendNewPairs(apiOut, markets)
							writeMarketsCache(apiCache, markets)
							// Update known markets atomically
							func() {
								// This is a simple approach - in production you might want a mutex
								known = markets
							}()
						} else {
							log.Printf("cycle: pairs_diff initial fetch: no new markets detected")
						}
					}

					// Continue with ticker
					for range ticker.C {
						log.Printf("cycle: pairs_diff checking for new markets...")
						markets, err := fetchUpbitMarkets(client)
						if err != nil {
							log.Printf("cycle: pairs_diff fetch error: %v", err)
							continue
						}
						log.Printf("cycle: pairs_diff fetched %d markets", len(markets))
						newOnes := diffNewMarkets(known, markets)
						if len(newOnes) > 0 {
							log.Printf("cycle: pairs_diff detected %d new markets", len(newOnes))
							appendNewPairs(apiOut, markets)
							writeMarketsCache(apiCache, markets)
							// Update known markets atomically
							func() {
								// This is a simple approach - in production you might want a mutex
								known = markets
							}()
						} else {
							log.Printf("cycle: pairs_diff no new markets detected")
						}
					}
				}()
			}
		}

		// Start service center source if enabled (runs independently)
		if cfg.Settings.ServiceCenter.Enabled {
			if cfg.Settings.ServiceCenter.Proxy {
				// Use proxy-enabled cycles similar to news
				if err := startServiceCenterClientCycles(outPath); err != nil {
					log.Printf("cycle: failed to start service_center client cycles: %v", err)
				}
				log.Printf("cycle: service_center client cycles started, running independently")
			} else {
				// Use single client without proxy
				go func() {
					serviceCenterInterval := time.Duration(cfg.Settings.ServiceCenter.IntervalMs) * time.Millisecond
					log.Printf("cycle: starting service_center cycle with interval %v", serviceCenterInterval)
					ticker := time.NewTicker(serviceCenterInterval)
					defer ticker.Stop()

					// First iteration immediately
					log.Printf("cycle: service_center initial fetch starting...")
					if err := runServiceCenterIteration(outPath, 0, "direct"); err != nil {
						log.Printf("cycle: service_center initial fetch error: %v", err)
					} else {
						log.Printf("cycle: service_center initial fetch successful")
					}

					// Continue with regular intervals
					for range ticker.C {
						log.Printf("cycle: service_center checking for new listings...")
						if err := runServiceCenterIteration(outPath, 0, "direct"); err != nil {
							log.Printf("cycle: service_center fetch error: %v", err)
						} else {
							log.Printf("cycle: service_center fetch successful")
						}
					}
				}()
			}
		}

		// Main cycle now just monitors and reports status
		log.Printf("cycle: all sources started, monitoring...")
		for {
			time.Sleep(30 * time.Second) // Check status every 30 seconds
			if cfg.Settings.AnnouncementsAPI.Enabled {
				log.Printf("cycle: announcements API source is running")
			}
			if cfg.Settings.MarketsAPI.Enabled {
				log.Printf("cycle: markets API source is running")
			}
			if cfg.Settings.ServiceCenter.Enabled {
				log.Printf("cycle: service_center source is running")
			}
		}
	}
}

// runIteration performs a single fetch-update pass without exiting the process.
func runIteration(outPath string) error {
	existing, existingSet := readCache(outPath)
	log.Printf("cache: loaded %d items from %s", len(existing), outPath)

	// Deduplicate existing cache by href (prefer entries with non-zero and newer timestamp)
	deduped, removed := dedupeCache(existing)
	if removed > 0 {
		existing = deduped
		existingSet = make(map[string]struct{}, len(existing))
		for _, it := range existing {
			existingSet[it.Href] = struct{}{}
		}
		if err := writeCache(outPath, existing); err != nil {
			return fmt.Errorf("failed to write cache after dedupe: %w", err)
		}
		log.Printf("cache: deduplicated %d entries; total now %d", removed, len(existing))
	}

	// Optional offline mode: only dedupe and exit without network calls
	if os.Getenv("UPBIT_DEDUPE_ONLY") == "1" {
		fmt.Printf("Deduplicated %d entries. Total: %d.\n", removed, len(existing))
		return nil
	}

	// Normalize timestamps to milliseconds and backfill where missing
	migrated := backfillTimestamps(existing)
	if migrated > 0 {
		log.Printf("cache: normalized/backfilled timestamps for %d items", migrated)
	}

	globalLatest, latestBySymbol := computeLatestTimestamps(existing)
	if !globalLatest.IsZero() {
		log.Printf("cache: latest timestamp = %s (KST)", globalLatest.Format("2006.01.02 15:04"))
	} else {
		log.Printf("cache: empty or no valid timestamps, full backfill will run")
	}

	// When cache is empty, write incrementally per page to allow resume under rate limits
	initiallyEmpty := len(existing) == 0
	var pageFlush func([]cacheItem)
	if initiallyEmpty {
		pageFlush = func(pageItems []cacheItem) {
			if len(pageItems) == 0 {
				return
			}
			// merge and persist incrementally
			existing = append(existing, pageItems...)
			for _, it := range pageItems {
				existingSet[it.Href] = struct{}{}
			}
			if err := writeCache(outPath, existing); err != nil {
				log.Printf("warn: failed to write incremental cache: %v", err)
			} else {
				log.Printf("cache: wrote incremental page, total cached now %d", len(existing))
			}
		}
	}

	// Load state from listing.json if exists
	lastID, lastHref := loadStateFromListing(outPath)

	// Do not query exchanges here; only separate command will recheck exchanges.
	var exchangeIndex map[string][]string
	newItems, newest, err := fetchAllNew(existingSet, globalLatest, latestBySymbol, pageFlush, lastID, exchangeIndex)
	if err != nil {
		return err
	}

	if len(newItems) == 0 {
		if migrated > 0 {
			if err := writeCache(outPath, existing); err != nil {
				return fmt.Errorf("failed to write cache after timestamp backfill: %w", err)
			}
			fmt.Printf("Updated timestamps for %d cached items. Total: %d.\n", migrated, len(existing))
			return nil
		}
		fmt.Println("No new announcements. Cache is up to date.")
		return nil
	}

	// Sort new items by time ascending for stable ordering
	sort.Slice(newItems, func(i, j int) bool { return newItems[i].Timestamp < newItems[j].Timestamp })

	// If we were flushing incrementally (empty cache at start), data is already written.
	if pageFlush != nil {
		fmt.Printf("Added %d new announcements. Total: %d.\n", len(newItems), len(existing))
		// Save state with newest item if any, otherwise keep previous
		saveLatestStateInListing(outPath, newest, lastID, lastHref)
		return nil
	}

	// Spawn non-blocking telegram signals after detection (does not wait; 1s delay inside goroutine)
	spawnTelegramSignals(newItems)

	// Optional trading on new listings
	if tradeEnabled {
		var err error
		if len(configuredAccounts) > 0 {
			err = handleTradingWithAccounts(newItems, configuredAccounts)
		} else {
			err = handleTradingOnNewListings(newItems)
		}
		if err != nil {
			log.Printf("trade: failed to handle new listings: %v", err)
		}
	}

	merged := append(existing, newItems...)
	if err := writeCache(outPath, merged); err != nil {
		return fmt.Errorf("failed to write cache: %w", err)
	}

	// Save state (latest new if exists, else previous)
	saveLatestStateInListing(outPath, newest, lastID, lastHref)
	fmt.Printf("Added %d new announcements. Total: %d.\n", len(newItems), len(merged))
	return nil
}

func readCache(path string) ([]cacheItem, map[string]struct{}) {
	f, err := os.Open(path)
	if err != nil {
		return []cacheItem{}, map[string]struct{}{}
	}
	defer f.Close()

	// Support both legacy (array) and new (object) formats
	dec := json.NewDecoder(f)
	dec.DisallowUnknownFields()
	var obj listingDocument
	if err := dec.Decode(&obj); err == nil && obj.Items != nil {
		items := obj.Items
		set := make(map[string]struct{}, len(items))
		for i := range items {
			set[items[i].Href] = struct{}{}
		}
		return items, set
	}
	// fallback try legacy array
	if _, err := f.Seek(0, 0); err == nil {
		var items []cacheItem
		dec2 := json.NewDecoder(f)
		if err2 := dec2.Decode(&items); err2 == nil {
			set := make(map[string]struct{}, len(items))
			for i := range items {
				set[items[i].Href] = struct{}{}
			}
			return items, set
		}
	}

	return []cacheItem{}, map[string]struct{}{}
}

func writeCache(path string, items []cacheItem) error {
	doc := listingDocument{Items: items}
	data, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		return err
	}
	data = append(data, '\n')
	// atomic write
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, "listing.json.*.tmp")
	if err != nil {
		return err
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	if _, err := tmp.Write(data); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return err
	}
	if err := tmp.Close(); err != nil {
		return err
	}
	return os.Rename(tmp.Name(), path)
}

func fetchAllNew(existingHrefSet map[string]struct{}, globalLatest time.Time, latestBySymbol map[string]time.Time, pageFlush func([]cacheItem), lastKnownID int, exchangeIndex map[string][]string) ([]cacheItem, *cacheItem, error) {
	page := 1
	perPage := 20

	var totalPages int = -1
	var collected []cacheItem
	var latestNew *cacheItem

	for {
		startReq := time.Now()
		resp, err := fetchPageAny(page, perPage)
		if err != nil {
			return nil, nil, err
		}

		if !resp.Success {
			return nil, nil, errors.New("API responded with success=false")
		}

		if totalPages == -1 {
			totalPages = resp.Data.TotalPages
		}

		log.Printf("page %d/%d fetched in %v, notices=%d", page, totalPages, time.Since(startReq).Truncate(time.Millisecond), len(resp.Data.Notices))

		var pageMin, pageMax time.Time
		reachedOld := false
		var pageNew []cacheItem

		for idx, n := range resp.Data.Notices {
			if !isTradeCategory(n.Category) {
				continue
			}
			href := getServiceCenterURL(n.ID)
			// membership by href is tracked in existingHrefSet, but early-stop is driven by timestamp below

			// Parse time to KST
			parsedTime := parseKST(n.ListedAt)
			if idx == 0 {
				pageMax = parsedTime
			}
			if pageMin.IsZero() || parsedTime.Before(pageMin) {
				pageMin = parsedTime
			}

			// Stop if reached last known ID
			if lastKnownID > 0 && n.ID == lastKnownID {
				reachedOld = true
				break
			}
			// Early stop: if item time is not newer than global latest, we can break out (pages are latest-first)
			if !globalLatest.IsZero() && (parsedTime.Equal(globalLatest) || parsedTime.Before(globalLatest)) {
				reachedOld = true
				break
			}

			symbol := extractSymbol(n.Title)
			if latest, ok := latestBySymbol[symbol]; ok {
				if !parsedTime.After(latest) {
					// Not newer for this symbol; skip
					continue
				}
			}

			item := cacheItem{
				Category:      strings.TrimSpace(n.Category),
				ListedAt:      n.ListedAt,
				FirstListedAt: n.FirstListedAt,
				ID:            n.ID,
				Href:          href,
				Symbol:        symbol,
				Title:         normalizeTitle(n.Title),
				Timestamp:     parsedTime.UnixMilli(),
			}
			// listing true only when title contains "Market Support for"
			if isListingTitle(item.Title) {
				item.Listing = true
				if ex := exchangeIndex[symbol]; len(ex) > 0 {
					item.Exchanges = ex
				}
			}
			item.SavedAtTimestamp = time.Now().UnixMilli()
			// explicit timestamps in ms from ISO fields
			if t := parseKST(item.ListedAt); !t.IsZero() {
				item.ListedAtTimestamp = t.UnixMilli()
			}
			if t := parseKST(item.FirstListedAt); !t.IsZero() {
				item.FirstListedAtTimestamp = t.UnixMilli()
			}
			fillDateStrings(&item)
			// Skip if we've already cached this href
			if _, ok := existingHrefSet[href]; ok {
				continue
			}
			collected = append(collected, item)
			pageNew = append(pageNew, item)
			if latestNew == nil {
				temp := item
				latestNew = &temp
			}
		}

		log.Printf("page %d summary: time range %s .. %s, new_added=%d, stop=%v",
			page,
			formatIfSet(pageMin),
			formatIfSet(pageMax),
			len(pageNew),
			reachedOld,
		)

		if pageFlush != nil && len(pageNew) > 0 {
			pageFlush(pageNew)
		}

		if reachedOld || page >= totalPages {
			break
		}
		page++
		// Gentle pacing to avoid rate limits (with jitter)
		sleepWithJitter(basePageDelay, pageDelayJitter)
	}

	return collected, latestNew, nil
}

// fetchPageAny performs a parallel race across the direct connection and all
// configured proxies. It retries with exponential backoff, returning the
// first successful response.
func fetchPageAny(page int, perPage int) (*apiResponse, error) {
	baseAPI := "https://api-manager.upbit.com/api/v1/announcements" // fallback
	if currentConfig != nil {
		baseAPI = currentConfig.GetAnnouncementsAPI()
	}
	url := fmt.Sprintf("%s?os=web&page=%d&per_page=%d&category=trade", baseAPI, page, perPage)
	backoff := 2 * time.Second
	const maxBackoff = 2 * time.Minute

	log.Printf("debug: fetching page %d with %d HTTP clients available", page, len(httpClients))

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		ctx, cancel := context.WithCancel(context.Background())
		type result struct {
			resp       *apiResponse
			err        error
			retryAfter time.Duration
			label      string
		}
		ch := make(chan result, len(httpClients))
		// compute stagger: about 100ms or evenly within 1s window across clients
		stagger := 100 * time.Millisecond
		if n := len(httpClients); n > 0 {
			slot := time.Second / time.Duration(n)
			if slot < stagger {
				stagger = slot
			}
		}
		// rotate priority so не всегда выигрывал direct: на каждой попытке стартуем без задержки с другого индекса
		startAt := (attempt - 1) % max(1, len(httpClients))
		for k := 0; k < len(httpClients); k++ {
			i := (startAt + k) % len(httpClients)
			hc := httpClients[i]
			label := "direct"
			if i < len(httpClientLabels) {
				label = httpClientLabels[i]
			}
			idx := i
			go func(cl *http.Client, lbl string, i int) {
				log.Printf("debug: client %s starting request (attempt %d)", lbl, attempt)
				// per-client delay to avoid synchronized bursts
				if i != startAt && stagger > 0 {
					select {
					case <-time.After(time.Duration(i) * stagger):
					case <-ctx.Done():
						ch <- result{err: ctx.Err(), label: lbl}
						return
					}
				}
				req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
				if err != nil {
					ch <- result{err: err, label: lbl}
					return
				}
				req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
				req.Header.Set("Accept-Language", "en-US,en;q=0.9")
				req.Header.Set("Cache-Control", "max-age=0")
				req.Header.Set("sec-ch-ua-platform", "\"macOS\"")

				res, err := cl.Do(req)
				if err != nil {
					log.Printf("debug: client %s request failed: %v", lbl, err)
					ch <- result{err: err, label: lbl}
					return
				}
				body, _ := io.ReadAll(res.Body)
				res.Body.Close()
				if res.StatusCode == http.StatusOK {
					var out apiResponse
					if err := json.Unmarshal(body, &out); err != nil {
						log.Printf("debug: client %s decode failed: %v", lbl, err)
						ch <- result{err: fmt.Errorf("decode failed: %w", err), label: lbl}
						return
					}
					log.Printf("debug: client %s successful response", lbl)
					ch <- result{resp: &out, label: lbl}
					return
				}
				var ra time.Duration
				if res.StatusCode == http.StatusTooManyRequests || (res.StatusCode >= 500 && res.StatusCode <= 599) {
					if v := res.Header.Get("Retry-After"); v != "" {
						if d, err := time.ParseDuration(v + "s"); err == nil {
							if d > 0 {
								if d > maxRetryAfter {
									ra = maxRetryAfter
								} else {
									ra = d
								}
							}
						}
					}
				}
				log.Printf("debug: client %s status %d: %s", lbl, res.StatusCode, string(body[:min(len(body), 200)]))
				ch <- result{err: fmt.Errorf("status %d: %s", res.StatusCode, string(body)), retryAfter: ra, label: lbl}
			}(hc, label, idx)
		}

		var chosen *apiResponse
		var winner string
		bestRetryAfter := time.Duration(0)
		for i := 0; i < len(httpClients); i++ {
			r := <-ch
			if r.resp != nil {
				chosen = r.resp
				winner = r.label
				break
			}
			if r.retryAfter > 0 {
				if bestRetryAfter == 0 || r.retryAfter < bestRetryAfter {
					bestRetryAfter = r.retryAfter
				}
			}
		}
		cancel()
		if chosen != nil {
			if winner == "" {
				winner = "unknown"
			}
			log.Printf("debug: successful response from client: %s", winner)
			return chosen, nil
		}
		if bestRetryAfter > 0 {
			log.Printf("debug: retry after %v", bestRetryAfter)
			time.Sleep(bestRetryAfter)
		} else {
			log.Printf("debug: attempt %d failed, backing off %v", attempt, backoff)
			time.Sleep(backoff)
			if backoff < maxBackoff {
				backoff *= 2
			}
		}
	}
	return nil, fmt.Errorf("all %d attempts failed", maxAttempts)
}

var symbolParenRegex = regexp.MustCompile(`\(([A-Z0-9\-]{2,})\)`) // text like (CYBER)

func extractSymbol(title string) string {
	// Try parentheses with uppercase letters/digits/hyphen
	if m := symbolParenRegex.FindStringSubmatch(title); len(m) == 2 {
		return m[1]
	}
	// Fallback: try to find last uppercase word
	words := strings.FieldsFunc(title, func(r rune) bool { return r == ' ' || r == ',' || r == '-' || r == '(' || r == ')' })
	for i := len(words) - 1; i >= 0; i-- {
		w := words[i]
		if w == strings.ToUpper(w) && len(w) >= 2 {
			return w
		}
	}
	return ""
}

func normalizeTitle(title string) string {
	return strings.TrimSpace(title)
}

// formatKST was unused and removed to satisfy linters

func parseKST(iso string) time.Time {
	t, err := time.Parse(time.RFC3339, iso)
	if err != nil {
		return time.Time{}
	}
	kst, err := time.LoadLocation("Asia/Seoul")
	if err == nil {
		t = t.In(kst)
	}
	return t
}

func computeLatestTimestamps(items []cacheItem) (time.Time, map[string]time.Time) {
	kst, _ := time.LoadLocation("Asia/Seoul")
	latestBySymbol := make(map[string]time.Time)
	var globalLatest time.Time
	for _, it := range items {
		var ts time.Time
		if it.Timestamp <= 0 {
			continue
		}
		// Interpret stored timestamp as milliseconds
		ts = time.UnixMilli(it.Timestamp).In(kst)
		if lt, ok := latestBySymbol[it.Symbol]; !ok || ts.After(lt) {
			latestBySymbol[it.Symbol] = ts
		}
		if ts.After(globalLatest) {
			globalLatest = ts
		}
	}
	return globalLatest, latestBySymbol
}

func formatIfSet(t time.Time) string {
	if t.IsZero() {
		return "-"
	}
	return t.Format("2006.01.02 15:04")
}

func backfillTimestamps(items []cacheItem) int {
	updated := 0
	for i := range items {
		// Normalize timestamp to milliseconds
		if items[i].Timestamp > 0 && items[i].Timestamp < 1_000_000_000_000 {
			items[i].Timestamp *= 1000
			updated++
		}
		// If timestamp missing, try to parse ISO listed_at (prefer listed_at over legacy date)
		if items[i].Timestamp <= 0 && items[i].ListedAt != "" {
			if t := parseKST(items[i].ListedAt); !t.IsZero() {
				items[i].Timestamp = t.UnixMilli()
				items[i].ListedAtTimestamp = t.UnixMilli()
				updated++
			}
		}
		// If timestamp is still missing, try to parse legacy date string as KST
		if items[i].Timestamp <= 0 && items[i].DateLegacy != "" {
			kst, _ := time.LoadLocation("Asia/Seoul")
			legacyLayout := "2006.01.02 15:04"
			if ts, err := time.ParseInLocation(legacyLayout, items[i].DateLegacy, kst); err == nil {
				items[i].Timestamp = ts.UnixMilli()
				// Fill listed_at in RFC3339 if missing
				if strings.TrimSpace(items[i].ListedAt) == "" {
					items[i].ListedAt = ts.In(kst).Format(time.RFC3339)
				}
				items[i].ListedAtTimestamp = ts.UnixMilli()
				updated++
			}
		}
		// Backfill first_listed_at timestamp if ISO present
		if items[i].FirstListedAt != "" && items[i].FirstListedAtTimestamp == 0 {
			if t := parseKST(items[i].FirstListedAt); !t.IsZero() {
				items[i].FirstListedAtTimestamp = t.UnixMilli()
				updated++
			}
		}
		// Fill date strings from timestamp if we have it
		if items[i].Timestamp > 0 {
			before := items[i]
			fillDateStrings(&items[i])
			// Remove legacy date field from output
			if items[i].DateLegacy != "" {
				items[i].DateLegacy = ""
				updated++
			}
			// If any field changed, count as update
			if before.DateUTC != items[i].DateUTC || before.DateMSK != items[i].DateMSK || before.DateKST != items[i].DateKST {
				updated++
			}
		}
	}
	return updated
}

func fillDateStrings(item *cacheItem) {
	if item.Timestamp <= 0 {
		return
	}
	utc := time.UnixMilli(item.Timestamp).UTC()
	moscow, _ := time.LoadLocation("Europe/Moscow")
	kst, _ := time.LoadLocation("Asia/Seoul")
	item.DateUTC = utc.Format("2006.01.02 15:04")
	item.DateMSK = utc.In(moscow).Format("2006.01.02 15:04")
	item.DateKST = utc.In(kst).Format("2006.01.02 15:04")
}

func isTradeCategory(cat string) bool {
	c := strings.TrimSpace(strings.ToLower(cat))
	if c == "trade" {
		return true
	}
	// Korean: 거래
	if strings.Contains(cat, "거래") {
		return true
	}
	return false
}

// isListingTitle returns true if the title is a listing announcement.
// The rule: contains substring "Market Support for" (case-sensitive per requirement).
func isListingTitle(title string) bool {
	return strings.Contains(title, "Market Support for")
}

func loadStateFromListing(path string) (int, string) {
	f, err := os.Open(path)
	if err != nil {
		return 0, ""
	}
	defer f.Close()
	var doc listingDocument
	if err := json.NewDecoder(f).Decode(&doc); err != nil {
		return 0, ""
	}
	return doc.LatestID, doc.LatestHref
}

func saveLatestStateInListing(path string, newest *cacheItem, prevID int, prevHref string) {
	// Read existing doc (or create empty)
	var doc listingDocument
	if b, err := os.ReadFile(path); err == nil && len(b) > 0 {
		_ = json.Unmarshal(b, &doc)
	}
	if doc.Items == nil {
		doc.Items = []cacheItem{}
	}
	if newest != nil {
		doc.LatestID = newest.ID
		doc.LatestHref = newest.Href
		doc.LatestListedAt = newest.ListedAt
	} else {
		doc.LatestID = prevID
		doc.LatestHref = prevHref
	}
	doc.SavedAt = time.Now().UTC().Format(time.RFC3339)
	// If Items is empty in doc but file exists separately, try to read items via readCache logic already done before writing
	data, _ := json.MarshalIndent(doc, "", "  ")
	// atomic write similar to writeCache
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, "listing.json.state.*.tmp")
	if err != nil {
		return
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	if _, err := tmp.Write(append(data, '\n')); err != nil {
		_ = tmp.Close()
		return
	}
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return
	}
	_ = tmp.Close()
	_ = os.Rename(tmp.Name(), path)
}

func minDuration(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func sleepWithJitter(base, jitter time.Duration) {
	if jitter <= 0 {
		time.Sleep(base)
		return
	}
	// random in [base-jitter, base+jitter]
	delta := rand.Int63n(int64(2*jitter)+1) - int64(jitter)
	d := time.Duration(int64(base) + delta)
	if d < 0 {
		d = base
	}
	time.Sleep(d)
}

func overrideConfigFromEnv() {
	if v := os.Getenv("UPBIT_MAX_ATTEMPTS"); v != "" {
		if n, err := parsePositiveInt(v); err == nil && n > 0 {
			maxAttempts = n
		}
	}
	if v := os.Getenv("UPBIT_MAX_RETRY_AFTER_SEC"); v != "" {
		if n, err := parsePositiveInt(v); err == nil && n > 0 {
			maxRetryAfter = time.Duration(n) * time.Second
		}
	}
	if v := os.Getenv("UPBIT_PAGE_DELAY_MS"); v != "" {
		if n, err := parsePositiveInt(v); err == nil && n > 0 {
			basePageDelay = time.Duration(n) * time.Millisecond
		}
	}
	if v := os.Getenv("UPBIT_PAGE_DELAY_JITTER_MS"); v != "" {
		if n, err := parsePositiveInt(v); err == nil && n >= 0 {
			pageDelayJitter = time.Duration(n) * time.Millisecond
		}
	}
	if v := os.Getenv("UPBIT_CYCLE_INTERVAL_SEC"); v != "" {
		if n, err := parsePositiveInt(v); err == nil && n > 0 {
			cycleInterval = time.Duration(n) * time.Second
		}
	}
}

func parsePositiveInt(s string) (int, error) {
	var n int
	_, err := fmt.Sscanf(s, "%d", &n)
	if err != nil {
		return 0, err
	}
	if n < 0 {
		n = -n
	}
	return n, nil
}

// hasArg checks if process arguments contain a specific flag
// removed: hasArg (unused)

// removed: buildExchangeIndex — exchange checks are handled by cmd/recheck-exchanges

// removed: uniqueAppend

// removed: mergeUniqueSorted

// removed: equalStringSlices

// removed: containsString

// removed: fetchBybitSpotBases

// removed: fetchBybitSwapBases

// removed: fetchBinanceSpotBases

// removed: fetchBinanceFuturesBases

// removed: fetchBingXSpotBases

// BingX swap bases
// removed: fetchBingXSwapBases

// Bitget spot symbols
// removed: fetchBitgetSpotBases

// Bitget swap bases (mix)
// removed: fetchBitgetSwapBases

// helpers for dynamic JSON
// removed: getString/getInt

// MEXC exchangeInfo compatible with Binance
// removed: fetchMexcSpotBases

// Lightweight direct checks to ensure presence when index missed
// removed: hasBingXSpotBaseQuick / hasMexcSpotBaseQuick

// MEXC swap bases
// removed: fetchMexcSwapBases

// KuCoin spot bases
// removed: fetchKucoinSpotBases

// KuCoin futures bases (USDT-M)
// removed: fetchKucoinFuturesBases

// dedupeCache removes duplicate entries by href, preferring entries
// with a non-zero timestamp, and if both have timestamps, prefers the newer.
func dedupeCache(items []cacheItem) ([]cacheItem, int) {
	hrefToIdx := make(map[string]int)
	deduped := make([]cacheItem, 0, len(items))
	removed := 0
	for _, it := range items {
		if prevIdx, exists := hrefToIdx[it.Href]; exists {
			prev := deduped[prevIdx]
			replace := false
			if prev.Timestamp == 0 && it.Timestamp > 0 {
				replace = true
			} else if prev.Timestamp > 0 && it.Timestamp > 0 && it.Timestamp > prev.Timestamp {
				replace = true
			}
			if replace {
				deduped[prevIdx] = it
			}
			removed++
			continue
		}
		hrefToIdx[it.Href] = len(deduped)
		deduped = append(deduped, it)
	}
	return deduped, removed
}

// ========================= Trading helpers =========================

// loadDotEnv loads environment variables from .env if present.
func loadDotEnv() {
	f, err := os.Open(".env")
	if err != nil {
		return
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		return
	}
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		eq := strings.IndexByte(line, '=')
		if eq <= 0 {
			continue
		}
		key := strings.TrimSpace(line[:eq])
		val := strings.TrimSpace(line[eq+1:])
		val = strings.Trim(val, "\"'")
		_ = os.Setenv(key, val)
	}
}

func handleTradingOnNewListings(items []cacheItem) error {
	if len(items) == 0 {
		return nil
	}
	var bybit *bybitClient
	if tradeOnBybit {
		apiKey := os.Getenv("BYBIT_API_KEY")
		apiSecret := os.Getenv("BYBIT_API_SECRET")
		bybit = newBybitClient(apiKey, apiSecret)
	}

	for _, it := range items {
		if !it.Listing {
			continue
		}
		base := strings.ToUpper(strings.TrimSpace(it.Symbol))
		if base == "" {
			continue
		}
		symbol := base + "USDT"
		cat := "linear"
		// Short-circuit in test mode to avoid network and assert event flow
		if os.Getenv("UPBIT_TEST_TRADING") == "1" {
			side := "Buy"
			if orderDirection == "short" {
				side = "Sell"
			}
			if tradeOnBybit {
				recordTestEvent("bybit:MARKET:" + side)
			}
			if tradeOnBingx {
				recordTestEvent("bingx:MARKET:" + side)
			}
			if len(orderTPPercents) > 0 {
				if tradeOnBybit {
					recordTestEvent("bybit:TP")
				}
				if tradeOnBingx {
					recordTestEvent("bingx:TP")
				}
			}
			if len(orderSLPercents) > 0 {
				if tradeOnBybit {
					recordTestEvent("bybit:SL")
				}
				if tradeOnBingx {
					recordTestEvent("bingx:SL")
				}
			}
			continue
		}
		var price float64
		var qtyStep, minQty, priceStep float64
		var haveMarket bool
		// Market discovery via Bybit public for now (BingX discovery TODO)
		if tradeOnBybit {
			if info, err := bybit.getInstrument(cat, symbol); err == nil {
				if p, err := bybit.getLastPrice(cat, symbol); err == nil && p > 0 {
					price = p
					qtyStep = info.LotSizeFilter.QtyStep
					minQty = info.LotSizeFilter.MinOrderQty
					priceStep = info.PriceFilter.TickSize
					haveMarket = true
				}
			}
		}
		if !haveMarket {
			log.Printf("trade: market data not resolved for %s; skipping", symbol)
			continue
		}
		// qtyStep, minQty, priceStep already set from market discovery

		qty := orderAmountUSDT / price
		qty = floorToStep(qty, qtyStep)
		if qty < minQty {
			log.Printf("trade: qty %.8f < min %.8f for %s, skipping", qty, minQty, symbol)
			continue
		}

		if tradeOnBybit {
			if err := bybit.setLeverage(cat, symbol, orderLeverage); err != nil {
				log.Printf("trade: set leverage failed for %s: %v", symbol, err)
			}
		}

		side := "Buy"
		if orderDirection == "short" {
			side = "Sell"
		}
		if tradeOnBybit {
			orderID, err := bybit.placeMarketOrder(cat, symbol, side, qty)
			if err != nil {
				log.Printf("trade: bybit market order failed for %s: %v", symbol, err)
				continue
			}
			log.Printf("trade: bybit opened %s %s qty=%.6f orderID=%s", side, symbol, qty, orderID)
		}

		avgPrice := price
		if tradeOnBybit {
			if pos, err := bybit.getPosition(cat, symbol); err == nil && pos.AvgPrice > 0 {
				avgPrice = pos.AvgPrice
			}
		}

		// Optionally attach SL/TP to the position (linked inside position)
		// Strategy: always attach SL if provided (closest one). Attach TP only when a single TP is provided.
		if len(orderSLPercents) > 0 || len(orderTPPercents) == 1 {
			var tpPtr *float64
			var slPtr *float64
			if len(orderTPPercents) == 1 {
				p := orderTPPercents[0]
				var tp float64
				if orderDirection == "long" {
					tp = avgPrice * (1 + p/100)
				} else {
					tp = avgPrice * (1 - p/100)
				}
				tpV := roundToStep(tp, priceStep)
				tpPtr = &tpV
			}
			if len(orderSLPercents) > 0 {
				closest := orderSLPercents[0]
				for _, v := range orderSLPercents {
					if v < closest {
						closest = v
					}
				}
				var sl float64
				if orderDirection == "long" {
					sl = avgPrice * (1 - closest/100)
				} else {
					sl = avgPrice * (1 + closest/100)
				}
				slV := roundToStep(sl, priceStep)
				slPtr = &slV
			}
			if tradeOnBybit {
				if err := bybit.setPositionTradingStop(cat, symbol, tpPtr, slPtr); err != nil {
					log.Printf("trade: failed to set position TP/SL for %s: %v", symbol, err)
				}
			}
		}

		// Place TPs as reduce-only orders only if we have more than one take-profit target
		if len(orderTPPercents) > 1 {
			portion := qty / float64(len(orderTPPercents))
			portion = floorToStep(portion, qtyStep)
			for _, p := range orderTPPercents {
				var tpPrice float64
				if orderDirection == "long" {
					tpPrice = avgPrice * (1 + p/100)
				} else {
					tpPrice = avgPrice * (1 - p/100)
				}
				tpPrice = roundToStep(tpPrice, priceStep)
				if tpPrice <= 0 || portion <= 0 {
					continue
				}
				oppSide := oppositeSide(side)
				if tradeOnBybit {
					if _, err := bybit.placeLimitReduceOnly(cat, symbol, oppSide, portion, tpPrice); err != nil {
						log.Printf("trade: TP order failed %s p=%.3f: %v", symbol, p, err)
					}
				}
			}
		}
		// Place SLs as reduce-only orders only if position-level SL not set (i.e., more than one provided -> we still place partial SLs)
		if len(orderSLPercents) > 1 {
			portion := qty / float64(len(orderSLPercents))
			portion = floorToStep(portion, qtyStep)
			for _, p := range orderSLPercents {
				var slTrig float64
				if orderDirection == "long" {
					slTrig = avgPrice * (1 - p/100)
				} else {
					slTrig = avgPrice * (1 + p/100)
				}
				slTrig = roundToStep(slTrig, priceStep)
				oppSide := oppositeSide(side)
				if tradeOnBybit {
					if _, err := bybit.placeStopMarketReduceOnly(cat, symbol, oppSide, portion, slTrig, orderDirection); err != nil {
						log.Printf("trade: SL order failed %s p=%.3f: %v", symbol, p, err)
					}
				}
			}
		}
	}
	return nil
}

// handleTradingWithAccounts performs trading per configured account, allowing
// account-specific credentials and risk settings. Currently supports Bybit.
func handleTradingWithAccounts(items []cacheItem, accounts []AccountConfig) error {
	if len(items) == 0 {
		return nil
	}
	var wg sync.WaitGroup
	for _, it := range items {
		if !it.Listing {
			continue
		}
		itCopy := it
		base := strings.ToUpper(strings.TrimSpace(itCopy.Symbol))
		if base == "" {
			continue
		}
		symbol := base + "USDT"
		category := "linear"

		for _, acc := range accounts {
			if !acc.Enabled {
				continue
			}
			accCopy := acc
			wg.Add(1)
			go func() {
				defer wg.Done()
				// Resolve per-account parameters with global fallbacks
				direction := accCopy.Direction
				if direction == "" {
					direction = orderDirection
				}
				amountUSDT := accCopy.AmountUSDT
				if amountUSDT <= 0 {
					amountUSDT = orderAmountUSDT
				}
				leverage := accCopy.Leverage
				if leverage <= 0 {
					leverage = orderLeverage
				}
				tpList := accCopy.TP
				if len(tpList) == 0 {
					tpList = orderTPPercents
				}
				slList := accCopy.SL
				if len(slList) == 0 {
					slList = orderSLPercents
				}

				if os.Getenv("UPBIT_TEST_TRADING") == "1" {
					side := "Buy"
					if direction == "short" {
						side = "Sell"
					}
					switch strings.ToLower(accCopy.Exchange) {
					case "bybit":
						recordTestEvent("bybit:MARKET:" + side)
						if len(tpList) > 0 {
							recordTestEvent("bybit:TP")
						}
						if len(slList) > 0 {
							recordTestEvent("bybit:SL")
						}
					case "bingx":
						recordTestEvent("bingx:MARKET:" + side)
						if len(tpList) > 0 {
							recordTestEvent("bingx:TP")
						}
						if len(slList) > 0 {
							recordTestEvent("bingx:SL")
						}
					}
					return
				}

				switch strings.ToLower(accCopy.Exchange) {
				case "bybit":
					by := newBybitClient(accCopy.APIKey, accCopy.APISecret)

					signalFoundTime := time.Now().UnixMilli()
					log.Printf("trade: bybit signal found at %d ms for %s", signalFoundTime, symbol)

					// Market discovery
					info, err := by.getInstrument(category, symbol)
					if err != nil {
						log.Printf("trade: bybit instrument not found %s: %v", symbol, err)
						return
					}
					price, err := by.getLastPrice(category, symbol)
					if err != nil || price <= 0 {
						log.Printf("trade: bybit ticker not found %s: %v", symbol, err)
						return
					}
					qtyStep := info.LotSizeFilter.QtyStep
					minQty := info.LotSizeFilter.MinOrderQty
					priceStep := info.PriceFilter.TickSize

					qty := amountUSDT / price
					qty = floorToStep(qty, qtyStep)
					if qty < minQty {
						log.Printf("trade: qty %.8f < min %.8f for %s, skipping", qty, minQty, symbol)
						return
					}
					if err := by.setLeverage(category, symbol, leverage); err != nil {
						log.Printf("trade: bybit set leverage failed for %s: %v", symbol, err)
					}
					side := "Buy"
					if direction == "short" {
						side = "Sell"
					}

					orderSentTime := time.Now().UnixMilli()
					orderID, err := by.placeMarketOrder(category, symbol, side, qty)
					if err != nil {
						log.Printf("trade: bybit market order failed for %s: %v", symbol, err)
						return
					}
					orderConfirmedTime := time.Now().UnixMilli()
					log.Printf("trade: bybit opened %s %s qty=%.6f orderID=%s (signal: %d ms, sent: %d ms, confirmed: %d ms)",
						side, symbol, qty, orderID, signalFoundTime, orderSentTime, orderConfirmedTime)
					avgPrice := price
					if pos, err := by.getPosition(category, symbol); err == nil && pos.AvgPrice > 0 {
						avgPrice = pos.AvgPrice
					}
					if len(slList) > 0 || len(tpList) == 1 {
						var tpPtr *float64
						var slPtr *float64
						if len(tpList) == 1 {
							p := tpList[0]
							var tp float64
							if direction == "long" {
								tp = avgPrice * (1 + p/100)
							} else {
								tp = avgPrice * (1 - p/100)
							}
							tpV := roundToStep(tp, priceStep)
							tpPtr = &tpV
						}
						if len(slList) > 0 {
							closest := slList[0]
							for _, v := range slList {
								if v < closest {
									closest = v
								}
							}
							var sl float64
							if direction == "long" {
								sl = avgPrice * (1 - closest/100)
							} else {
								sl = avgPrice * (1 + closest/100)
							}
							slV := roundToStep(sl, priceStep)
							slPtr = &slV
						}
						if err := by.setPositionTradingStop(category, symbol, tpPtr, slPtr); err != nil {
							log.Printf("trade: bybit set TP/SL failed for %s: %v", symbol, err)
						}
					}
					if len(tpList) > 1 {
						portion := qty / float64(len(tpList))
						portion = floorToStep(portion, qtyStep)
						for _, p := range tpList {
							var tpPrice float64
							if direction == "long" {
								tpPrice = avgPrice * (1 + p/100)
							} else {
								tpPrice = avgPrice * (1 - p/100)
							}
							tpPrice = roundToStep(tpPrice, priceStep)
							if tpPrice <= 0 || portion <= 0 {
								continue
							}
							oppSide := oppositeSide(side)
							if _, err := by.placeLimitReduceOnly(category, symbol, oppSide, portion, tpPrice); err != nil {
								log.Printf("trade: bybit TP order failed %s p=%.3f: %v", symbol, p, err)
							}
						}
					}
					if len(slList) > 1 {
						portion := qty / float64(len(slList))
						portion = floorToStep(portion, qtyStep)
						for _, p := range slList {
							var slTrig float64
							if direction == "long" {
								slTrig = avgPrice * (1 - p/100)
							} else {
								slTrig = avgPrice * (1 + p/100)
							}
							slTrig = roundToStep(slTrig, priceStep)
							oppSide := oppositeSide(side)
							if _, err := by.placeStopMarketReduceOnly(category, symbol, oppSide, portion, slTrig, direction); err != nil {
								log.Printf("trade: bybit SL order failed %s p=%.3f: %v", symbol, p, err)
							}
						}
					}

					// Add to trailing stop monitoring if enabled
					if accCopy.TrailingEnabled && accCopy.TrailingPercent > 0 {
						initialSL := 0.0
						if len(slList) > 0 {
							closest := slList[0]
							for _, v := range slList {
								if v < closest {
									closest = v
								}
							}
							if direction == "long" {
								initialSL = avgPrice * (1 - closest/100)
							} else {
								initialSL = avgPrice * (1 + closest/100)
							}
							initialSL = roundToStep(initialSL, priceStep)
						}

						trailingPos := trailingStopPosition{
							Symbol:          symbol,
							Exchange:        "bybit",
							Direction:       direction,
							EntryPrice:      avgPrice,
							CurrentSL:       initialSL,
							TrailingPercent: accCopy.TrailingPercent,
							Quantity:        qty,
							Category:        category,
							APIKey:          accCopy.APIKey,
							APISecret:       accCopy.APISecret,
							AccountName:     accCopy.Name,
						}
						addTrailingStop(trailingPos)
					}

					// After orders are placed, optionally log to per-account Telegram logs chat (non-blocking)
					if accCopy.TelegramEnabled && accCopy.TelegramLogsChat != 0 && strings.TrimSpace(accCopy.TelegramBotToken) != "" {
						side := "LONG"
						if strings.ToLower(direction) == "short" {
							side = "SHORT"
						}
						trailingInfo := ""
						if accCopy.TrailingEnabled && accCopy.TrailingPercent > 0 {
							trailingInfo = fmt.Sprintf(" TRAIL=%.1f%%", accCopy.TrailingPercent)
						}
						text := fmt.Sprintf("%s | %s %s qty=%.6f lev=%dx TP=%v SL=%v%s (signal: %d ms, sent: %d ms, confirmed: %d ms)",
							accCopy.Name, symbol, side, qty, leverage, tpList, slList, trailingInfo, signalFoundTime, orderSentTime, orderConfirmedTime)
						bot := strings.TrimSpace(accCopy.TelegramBotToken)
						chat := accCopy.TelegramLogsChat
						go func() {
							if err := sendTelegramMessage(context.Background(), bot, chat, text); err != nil {
								log.Printf("telegram: account log send failed: %v", err)
							}
						}()
					}

				case "bingx":
					bx := newBingxClient(accCopy.APIKey, accCopy.APISecret)
					// Use Bybit for market discovery (BingX doesn't have good public API for this)
					by := newBybitClient("", "")
					info, err := by.getInstrument(category, symbol)
					if err != nil {
						log.Printf("trade: bingx instrument not found %s: %v", symbol, err)
						return
					}
					price, err := by.getLastPrice(category, symbol)
					if err != nil || price <= 0 {
						log.Printf("trade: bingx ticker not found %s: %v", symbol, err)
						return
					}
					qtyStep := info.LotSizeFilter.QtyStep
					minQty := info.LotSizeFilter.MinOrderQty
					priceStep := info.PriceFilter.TickSize

					qty := amountUSDT / price
					qty = floorToStep(qty, qtyStep)
					if qty < minQty {
						log.Printf("trade: qty %.8f < min %.8f for %s, skipping", qty, minQty, symbol)
						return
					}

					bingxSymbol := base + "-USDT"
					bingxSide := "BUY"
					if direction == "short" {
						bingxSide = "SELL"
					}

					signalFoundTime := time.Now().UnixMilli()
					log.Printf("trade: bingx signal found at %d ms for %s", signalFoundTime, symbol)

					orderSentTime := time.Now().UnixMilli()
					orderID, err := bx.placeMarketOrder(bingxSymbol, bingxSide, qty)
					if err != nil {
						log.Printf("trade: bingx market order failed for %s: %v", symbol, err)
						return
					}
					orderConfirmedTime := time.Now().UnixMilli()
					log.Printf("trade: bingx opened %s %s qty=%.6f orderID=%s (signal: %d ms, sent: %d ms, confirmed: %d ms)",
						bingxSide, bingxSymbol, qty, orderID, signalFoundTime, orderSentTime, orderConfirmedTime)

					avgPrice := price

					// Place TP orders
					if len(tpList) > 0 {
						portion := qty
						if len(tpList) > 1 {
							portion = floorToStep(qty/float64(len(tpList)), qtyStep)
						}
						bingxCloseSide := "SELL"
						posSide := "LONG"
						if direction == "short" {
							bingxCloseSide = "BUY"
							posSide = "SHORT"
						}
						for _, p := range tpList {
							var stopPrice float64
							if direction == "long" {
								stopPrice = avgPrice * (1 + p/100)
							} else {
								stopPrice = avgPrice * (1 - p/100)
							}
							stopPrice = roundToStep(stopPrice, priceStep)
							if _, err := bx.placeConditionalMarket(bingxSymbol, bingxCloseSide, posSide, "TAKE_PROFIT_MARKET", portion, stopPrice); err != nil {
								log.Printf("trade: bingx TP failed p=%.3f: %v", p, err)
							}
						}
					}

					// Place SL orders
					if len(slList) > 0 {
						portion := qty
						if len(slList) > 1 {
							portion = floorToStep(qty/float64(len(slList)), qtyStep)
						}
						bingxCloseSide := "SELL"
						posSide := "LONG"
						if direction == "short" {
							bingxCloseSide = "BUY"
							posSide = "SHORT"
						}
						for _, p := range slList {
							var stopPrice float64
							if direction == "long" {
								stopPrice = avgPrice * (1 - p/100)
							} else {
								stopPrice = avgPrice * (1 + p/100)
							}
							stopPrice = roundToStep(stopPrice, priceStep)
							if _, err := bx.placeConditionalMarket(bingxSymbol, bingxCloseSide, posSide, "STOP_MARKET", portion, stopPrice); err != nil {
								log.Printf("trade: bingx SL failed p=%.3f: %v", p, err)
							}
						}
					}

					// Add to trailing stop monitoring if enabled
					if accCopy.TrailingEnabled && accCopy.TrailingPercent > 0 {
						initialSL := 0.0
						if len(slList) > 0 {
							closest := slList[0]
							for _, v := range slList {
								if v < closest {
									closest = v
								}
							}
							if direction == "long" {
								initialSL = avgPrice * (1 - closest/100)
							} else {
								initialSL = avgPrice * (1 + closest/100)
							}
							initialSL = roundToStep(initialSL, priceStep)
						}

						posSide := "LONG"
						if direction == "short" {
							posSide = "SHORT"
						}

						trailingPos := trailingStopPosition{
							Symbol:          symbol,
							Exchange:        "bingx",
							Direction:       direction,
							EntryPrice:      avgPrice,
							CurrentSL:       initialSL,
							TrailingPercent: accCopy.TrailingPercent,
							Quantity:        qty,
							Category:        category,
							APIKey:          accCopy.APIKey,
							APISecret:       accCopy.APISecret,
							AccountName:     accCopy.Name,
							BingxSymbol:     bingxSymbol,
							PositionSide:    posSide,
						}
						addTrailingStop(trailingPos)
					}

					// After orders are placed, optionally log to per-account Telegram logs chat (non-blocking)
					if accCopy.TelegramEnabled && accCopy.TelegramLogsChat != 0 && strings.TrimSpace(accCopy.TelegramBotToken) != "" {
						side := "LONG"
						if strings.ToLower(direction) == "short" {
							side = "SHORT"
						}
						trailingInfo := ""
						if accCopy.TrailingEnabled && accCopy.TrailingPercent > 0 {
							trailingInfo = fmt.Sprintf(" TRAIL=%.1f%%", accCopy.TrailingPercent)
						}
						text := fmt.Sprintf("%s | %s %s qty=%.6f lev=%dx TP=%v SL=%v%s (signal: %d ms, sent: %d ms, confirmed: %d ms)",
							accCopy.Name, bingxSymbol, side, qty, leverage, tpList, slList, trailingInfo, signalFoundTime, orderSentTime, orderConfirmedTime)
						bot := strings.TrimSpace(accCopy.TelegramBotToken)
						chat := accCopy.TelegramLogsChat
						go func() {
							if err := sendTelegramMessage(context.Background(), bot, chat, text); err != nil {
								log.Printf("telegram: account log send failed: %v", err)
							}
						}()
					}

				default:
					log.Printf("trade: unsupported exchange '%s' for account '%s'", accCopy.Exchange, accCopy.Name)
				}
			}()
		}
	}
	wg.Wait()
	return nil
}

// validateAccountsConfig ensures each enabled account is sane, using fallbacks
// from global flags when account fields are omitted.
func validateAccountsConfig(accounts []AccountConfig, fallbackDirection string, fallbackAmountUSDT float64, fallbackLeverage int) error {
	if len(accounts) == 0 {
		return errors.New("no accounts configured")
	}
	for _, acc := range accounts {
		if !acc.Enabled {
			continue
		}
		ex := strings.ToLower(strings.TrimSpace(acc.Exchange))
		if ex == "" {
			return fmt.Errorf("account '%s': exchange is required", acc.Name)
		}
		dir := acc.Direction
		if dir == "" {
			dir = fallbackDirection
		}
		if dir != "long" && dir != "short" {
			return fmt.Errorf("account '%s': direction must be long or short", acc.Name)
		}
		amt := acc.AmountUSDT
		if amt <= 0 {
			amt = fallbackAmountUSDT
		}
		if amt <= 0 {
			return fmt.Errorf("account '%s': amount_usdt must be > 0 (or set global)", acc.Name)
		}
		lev := acc.Leverage
		if lev <= 0 {
			lev = fallbackLeverage
		}
		if lev < 1 {
			return fmt.Errorf("account '%s': leverage must be >= 1 (or set global)", acc.Name)
		}
		switch ex {
		case "bybit":
			if strings.TrimSpace(acc.APIKey) == "" || strings.TrimSpace(acc.APISecret) == "" {
				return fmt.Errorf("account '%s': api_key/api_secret required for Bybit", acc.Name)
			}
		case "bingx":
			// Not implemented, but allow presence; recommend keys if provided
			if strings.TrimSpace(acc.APIKey) == "" || strings.TrimSpace(acc.APISecret) == "" {
				log.Printf("warn: account '%s' (BingX) missing api_key/secret; execution not implemented anyway", acc.Name)
			}
		default:
			return fmt.Errorf("account '%s': unsupported exchange '%s'", acc.Name, acc.Exchange)
		}
	}
	return nil
}

func oppositeSide(side string) string {
	if side == "Buy" {
		return "Sell"
	}
	return "Buy"
}

func floorToStep(value, step float64) float64 {
	if step <= 0 {
		return value
	}
	n := int(value / step)
	return float64(n) * step
}

func roundToStep(value, step float64) float64 {
	if step <= 0 {
		return value
	}
	n := int((value / step) + 0.5)
	return float64(n) * step
}

// ========================= Trailing Stop Functions =========================

// addTrailingStop adds a new position to trailing stop monitoring
func addTrailingStop(pos trailingStopPosition) {
	trailingStopsMu.Lock()
	defer trailingStopsMu.Unlock()

	// Initialize price tracking based on direction
	if pos.Direction == "long" {
		pos.HighestPrice = pos.EntryPrice
	} else {
		pos.LowestPrice = pos.EntryPrice
	}

	activeTrailingStops = append(activeTrailingStops, pos)
	log.Printf("trailing: added %s %s position for %s, entry=%.8f, trailing=%.2f%%",
		pos.Exchange, pos.Symbol, pos.Direction, pos.EntryPrice, pos.TrailingPercent)
}

// startTrailingStopMonitor starts the trailing stop monitoring goroutine
func startTrailingStopMonitor() {
	go func() {
		ticker := time.NewTicker(5 * time.Second) // Check every 5 seconds
		defer ticker.Stop()

		for range ticker.C {
			updateTrailingStops()
		}
	}()
}

// updateTrailingStops checks all active trailing stops and updates them if needed
func updateTrailingStops() {
	trailingStopsMu.Lock()
	defer trailingStopsMu.Unlock()

	for i := len(activeTrailingStops) - 1; i >= 0; i-- {
		pos := &activeTrailingStops[i]

		// Get current price
		currentPrice, err := getCurrentPrice(pos.Exchange, pos.Symbol, pos.Category)
		if err != nil {
			log.Printf("trailing: failed to get price for %s %s: %v", pos.Exchange, pos.Symbol, err)
			continue
		}

		shouldUpdate, newSL := calculateNewStopLoss(pos, currentPrice)
		if shouldUpdate {
			if updatePositionStopLoss(pos, newSL) {
				pos.CurrentSL = newSL
				log.Printf("trailing: updated %s %s SL from %.8f to %.8f (price: %.8f)",
					pos.Exchange, pos.Symbol, pos.CurrentSL, newSL, currentPrice)
			}
		}
	}
}

// calculateNewStopLoss determines if stop loss should be updated and returns new SL price
func calculateNewStopLoss(pos *trailingStopPosition, currentPrice float64) (bool, float64) {
	if pos.Direction == "long" {
		// For long positions, trail when price goes up
		if currentPrice > pos.HighestPrice {
			pos.HighestPrice = currentPrice
			newSL := currentPrice * (1 - pos.TrailingPercent/100)
			if newSL > pos.CurrentSL {
				return true, newSL
			}
		}
	} else {
		// For short positions, trail when price goes down
		if currentPrice < pos.LowestPrice {
			pos.LowestPrice = currentPrice
			newSL := currentPrice * (1 + pos.TrailingPercent/100)
			if newSL < pos.CurrentSL {
				return true, newSL
			}
		}
	}
	return false, 0
}

// getCurrentPrice gets current price for a symbol from the appropriate exchange
func getCurrentPrice(exchange, symbol, category string) (float64, error) {
	switch strings.ToLower(exchange) {
	case "bybit":
		client := newBybitClient("", "") // Public endpoint, no keys needed
		return client.getLastPrice(category, symbol)
	case "bingx":
		// For BingX, we use Bybit public API for price discovery as before
		client := newBybitClient("", "")
		return client.getLastPrice(category, symbol)
	default:
		return 0, fmt.Errorf("unsupported exchange: %s", exchange)
	}
}

// updatePositionStopLoss updates the stop loss for a position
func updatePositionStopLoss(pos *trailingStopPosition, newSL float64) bool {
	switch strings.ToLower(pos.Exchange) {
	case "bybit":
		client := newBybitClient(pos.APIKey, pos.APISecret)
		err := client.updateStopLoss(pos.Category, pos.Symbol, newSL)
		if err != nil {
			log.Printf("trailing: failed to update Bybit SL for %s: %v", pos.Symbol, err)
			return false
		}
		return true
	case "bingx":
		client := newBingxClient(pos.APIKey, pos.APISecret)
		side := "SELL"
		if pos.Direction == "short" {
			side = "BUY"
		}
		_, err := client.updateStopLoss(pos.BingxSymbol, side, pos.PositionSide, pos.Quantity, newSL)
		if err != nil {
			log.Printf("trailing: failed to update BingX SL for %s: %v", pos.Symbol, err)
			return false
		}
		return true
	default:
		log.Printf("trailing: unsupported exchange for SL update: %s", pos.Exchange)
		return false
	}
}

// ---------------- Bybit client (v5) ---------------

type bybitClient struct {
	apiKey    string
	apiSecret string
	hc        *http.Client
}

func newBybitClient(key, secret string) *bybitClient {
	return &bybitClient{apiKey: key, apiSecret: secret, hc: &http.Client{Timeout: 15 * time.Second}}
}

const bybitBase = "https://api.bybit.com"

// signing: sign = HMAC_SHA256(secret, timestamp+apiKey+recvWindow+body)
func (c *bybitClient) signedPOST(path string, body string) ([]byte, error) {
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	recvWindow := "5000"
	preSign := timestamp + c.apiKey + recvWindow + body
	mac := hmac.New(sha256.New, []byte(c.apiSecret))
	_, _ = mac.Write([]byte(preSign))
	signature := hex.EncodeToString(mac.Sum(nil))

	req, err := http.NewRequest(http.MethodPost, bybitBase+path, strings.NewReader(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-BAPI-API-KEY", c.apiKey)
	req.Header.Set("X-BAPI-SIGN", signature)
	req.Header.Set("X-BAPI-TIMESTAMP", timestamp)
	req.Header.Set("X-BAPI-RECV-WINDOW", recvWindow)

	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bybit %s: %s", res.Status, string(b))
	}
	return b, nil
}

func (c *bybitClient) publicGET(path string, query string) ([]byte, error) {
	url := bybitBase + path
	if query != "" {
		url += "?" + query
	}
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bybit %s: %s", res.Status, string(b))
	}
	return b, nil
}

type bybitInstrumentInfo struct {
	Symbol        string `json:"symbol"`
	LotSizeFilter struct {
		MinOrderQty float64 `json:"minOrderQty,string"`
		QtyStep     float64 `json:"qtyStep,string"`
	} `json:"lotSizeFilter"`
	PriceFilter struct {
		TickSize float64 `json:"tickSize,string"`
	} `json:"priceFilter"`
}

func (c *bybitClient) getInstrument(category, symbol string) (*bybitInstrumentInfo, error) {
	b, err := c.publicGET("/v5/market/instruments-info", fmt.Sprintf("category=%s&symbol=%s", category, symbol))
	if err != nil {
		return nil, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []bybitInstrumentInfo `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return nil, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return nil, fmt.Errorf("bybit: instrument not found: %s (%s)", symbol, resp.RetMsg)
	}
	info := resp.Result.List[0]
	return &info, nil
}

func (c *bybitClient) getLastPrice(category, symbol string) (float64, error) {
	b, err := c.publicGET("/v5/market/tickers", fmt.Sprintf("category=%s&symbol=%s", category, symbol))
	if err != nil {
		return 0, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []struct {
				LastPrice float64 `json:"lastPrice,string"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return 0, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return 0, fmt.Errorf("bybit: ticker not found: %s (%s)", symbol, resp.RetMsg)
	}
	return resp.Result.List[0].LastPrice, nil
}

func (c *bybitClient) setLeverage(category, symbol string, leverage int) error {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","buyLeverage":"%d","sellLeverage":"%d"}`, category, symbol, leverage, leverage)
	_, err := c.signedPOST("/v5/position/set-leverage", body)
	return err
}

func (c *bybitClient) placeMarketOrder(category, symbol, side string, qty float64) (string, error) {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Market","qty":"%.8f","timeInForce":"IOC"}`, category, symbol, side, qty)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

func (c *bybitClient) placeLimitReduceOnly(category, symbol, side string, qty, price float64) (string, error) {
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Limit","qty":"%.8f","price":"%.8f","timeInForce":"GTC","reduceOnly":true}`, category, symbol, side, qty, price)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

// orderDirection here is user's logical direction: long/short. Affects triggerDirection.
func (c *bybitClient) placeStopMarketReduceOnly(category, symbol, side string, qty, triggerPrice float64, orderDirection string) (string, error) {
	triggerDirection := 2 // default: fall below
	if orderDirection == "short" {
		triggerDirection = 1 // rise above
	}
	body := fmt.Sprintf(`{"category":"%s","symbol":"%s","side":"%s","orderType":"Market","qty":"%.8f","reduceOnly":true,"triggerDirection":%d,"triggerPrice":"%.8f","timeInForce":"GTC"}`, category, symbol, side, qty, triggerDirection, triggerPrice)
	b, err := c.signedPOST("/v5/order/create", body)
	if err != nil {
		return "", err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			OrderId string `json:"orderId"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.RetCode != 0 {
		return "", fmt.Errorf("bybit order error: %s", resp.RetMsg)
	}
	return resp.Result.OrderId, nil
}

type bybitPosition struct {
	AvgPrice float64
}

func (c *bybitClient) getPosition(category, symbol string) (*bybitPosition, error) {
	b, err := c.signedPOST("/v5/position/list", fmt.Sprintf(`{"category":"%s","symbol":"%s"}`, category, symbol))
	if err != nil {
		return nil, err
	}
	var resp struct {
		RetCode int    `json:"retCode"`
		RetMsg  string `json:"retMsg"`
		Result  struct {
			List []struct {
				AvgPrice float64 `json:"avgPrice,string"`
			} `json:"list"`
		} `json:"result"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return nil, err
	}
	if resp.RetCode != 0 || len(resp.Result.List) == 0 {
		return nil, fmt.Errorf("bybit position error: %s", resp.RetMsg)
	}
	return &bybitPosition{AvgPrice: resp.Result.List[0].AvgPrice}, nil
}

// setPositionTradingStop sets TP/SL attached to the current position (v5: /v5/position/trading-stop)
// Pass nil for tp or sl to skip the respective field
func (c *bybitClient) setPositionTradingStop(category, symbol string, tp, sl *float64) error {
	// Build minimal JSON with only provided fields
	parts := []string{fmt.Sprintf("\"category\":\"%s\"", category), fmt.Sprintf("\"symbol\":\"%s\"", symbol)}
	if tp != nil {
		parts = append(parts, fmt.Sprintf("\"takeProfit\":\"%.8f\"", *tp))
	}
	if sl != nil {
		parts = append(parts, fmt.Sprintf("\"stopLoss\":\"%.8f\"", *sl))
	}
	body := "{" + strings.Join(parts, ",") + "}"
	_, err := c.signedPOST("/v5/position/trading-stop", body)
	return err
}

// updateStopLoss updates only the stop loss for an existing position
func (c *bybitClient) updateStopLoss(category, symbol string, newSL float64) error {
	return c.setPositionTradingStop(category, symbol, nil, &newSL)
}

// ---------------- BingX client (Perp v2) ----------------

type bingxClient struct {
	apiKey    string
	apiSecret string
	hc        *http.Client
}

func newBingxClient(key, secret string) *bingxClient {
	return &bingxClient{apiKey: key, apiSecret: secret, hc: &http.Client{Timeout: 15 * time.Second}}
}

const bingxBase = "https://open-api.bingx.com"

func canonicalize(params map[string]string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var parts []string
	for _, k := range keys {
		parts = append(parts, k+"="+params[k])
	}
	return strings.Join(parts, "&")
}

func canonicalizeEncoded(params map[string]string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var parts []string
	for _, k := range keys {
		parts = append(parts, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}
	return strings.Join(parts, "&")
}

func (c *bingxClient) signedPOST(path string, params map[string]string) ([]byte, error) {
	if params == nil {
		params = make(map[string]string)
	}
	params["apiKey"] = c.apiKey
	ts := fmt.Sprintf("%d", time.Now().UnixMilli())
	params["timestamp"] = ts
	params["recvWindow"] = "5000"
	// Sign only canonical parameters (sorted, not URL-encoded)
	origin := canonicalize(params)
	mac := hmac.New(sha256.New, []byte(c.apiSecret))
	_, _ = mac.Write([]byte(origin))
	sign := hex.EncodeToString(mac.Sum(nil))
	// Send signature in body as 'signature'
	params["signature"] = sign
	bodyQS := canonicalizeEncoded(params)
	urlStr := bingxBase + path
	req, err := http.NewRequest(http.MethodPost, urlStr, strings.NewReader(bodyQS))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("X-BX-APIKEY", c.apiKey)
	// Timestamp usually not required as header when present in body
	res, err := c.hc.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	b, _ := io.ReadAll(res.Body)
	if res.StatusCode/100 != 2 {
		return nil, fmt.Errorf("bingx %s: %s", res.Status, string(b))
	}
	return b, nil
}

func (c *bingxClient) placeMarketOrder(symbol, side string, qty float64) (string, error) {
	params := map[string]string{
		"symbol":   symbol,
		"side":     strings.ToUpper(side),
		"type":     "MARKET",
		"quantity": fmt.Sprintf("%.8f", qty),
		"positionSide": func() string {
			if strings.EqualFold(side, "BUY") {
				return "LONG"
			}
			return "SHORT"
		}(),
	}
	b, err := c.signedPOST("/openApi/swap/v2/trade/order", params)
	if err != nil {
		return "", err
	}
	var resp struct {
		Code int `json:"code"`
		Data struct {
			OrderId string `json:"orderId"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.Code != 0 {
		return "", fmt.Errorf("bingx order error: %s", resp.Msg)
	}
	return resp.Data.OrderId, nil
}

func (c *bingxClient) placeConditionalMarket(symbol, side, positionSide, orderType string, qty, stopPrice float64) (string, error) {
	params := map[string]string{
		"symbol":       symbol,
		"side":         strings.ToUpper(side),
		"positionSide": strings.ToUpper(positionSide),
		"type":         strings.ToUpper(orderType),
		"quantity":     fmt.Sprintf("%.8f", qty),
		"stopPrice":    fmt.Sprintf("%.8f", stopPrice),
		"workingType":  "MARK_PRICE",
	}
	b, err := c.signedPOST("/openApi/swap/v2/trade/order", params)
	if err != nil {
		return "", err
	}
	var resp struct {
		Code int `json:"code"`
		Data struct {
			OrderId string `json:"orderId"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	if err := json.Unmarshal(b, &resp); err != nil {
		return "", err
	}
	if resp.Code != 0 {
		return "", fmt.Errorf("bingx order error: %s", resp.Msg)
	}
	return resp.Data.OrderId, nil
}

// updateStopLoss updates stop loss for BingX position by placing a new conditional order
// Note: For BingX, we place a new stop loss order. The old one should be cancelled separately if needed.
func (c *bingxClient) updateStopLoss(symbol, side, positionSide string, qty, newSL float64) (string, error) {
	return c.placeConditionalMarket(symbol, side, positionSide, "STOP_MARKET", qty, newSL)
}

// ========================= URL helpers =========================

func getServiceCenterURL(id int) string {
	serviceCenterBase := "https://upbit.com/service_center/notice" // fallback
	if currentConfig != nil {
		serviceCenterBase = currentConfig.GetServiceCenterURL()
	}
	return fmt.Sprintf("%s?id=%d&lang=en-US", serviceCenterBase, id)
}

// ========================= API source helpers (listing_api.json) =========================

type upbitMarket struct {
	Market        string `json:"market"`
	KoreanName    string `json:"korean_name"`
	EnglishName   string `json:"english_name"`
	MarketWarning string `json:"market_warning"`
}

type marketsCache struct {
	Markets []string `json:"markets"`
	SavedAt string   `json:"saved_at,omitempty"`
}

type apiPairItem struct {
	Market           string `json:"market"`
	Base             string `json:"base"`
	Quote            string `json:"quote"`
	FoundAtTimestamp int64  `json:"found_at_timestamp"`
	DateUTC          string `json:"date_utc,omitempty"`
	DateMSK          string `json:"date_msk,omitempty"`
	DateKST          string `json:"date_kst,omitempty"`
}

type apiPairsDocument struct {
	Items   []apiPairItem `json:"items"`
	SavedAt string        `json:"saved_at,omitempty"`
}

func fetchUpbitMarkets(client *http.Client) ([]string, error) {
	marketsAPI := "https://api.upbit.com/v1/market/all" // fallback
	if currentConfig != nil {
		marketsAPI = currentConfig.GetMarketsAPI()
	}
	url := marketsAPI + "?isDetails=false"
	req, _ := http.NewRequest(http.MethodGet, url, nil)
	req.Header.Set("User-Agent", "upbit-listing/1.0")

	// Add context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(res.Body)
		return nil, fmt.Errorf("upbit %s: %s", res.Status, string(b))
	}
	var rows []upbitMarket
	if err := json.NewDecoder(res.Body).Decode(&rows); err != nil {
		return nil, err
	}
	set := make(map[string]struct{}, len(rows))
	for _, r := range rows {
		m := strings.ToUpper(strings.TrimSpace(r.Market))
		if m != "" {
			set[m] = struct{}{}
		}
	}
	out := make([]string, 0, len(set))
	for m := range set {
		out = append(out, m)
	}
	sort.Strings(out)
	return out, nil
}

func diffNewMarkets(prev, curr []string) []string {
	prevSet := make(map[string]struct{}, len(prev))
	for _, m := range prev {
		prevSet[m] = struct{}{}
	}
	var out []string
	for _, m := range curr {
		if _, ok := prevSet[m]; !ok {
			out = append(out, m)
		}
	}
	return out
}

func appendNewPairs(path string, markets []string) {
	var doc apiPairsDocument
	if b, err := os.ReadFile(path); err == nil && len(b) > 0 {
		_ = json.Unmarshal(b, &doc)
	}
	if doc.Items == nil {
		doc.Items = []apiPairItem{}
	}
	now := time.Now().UTC()
	for _, m := range markets {
		base, quote := splitMarket(m)
		item := apiPairItem{
			Market:           m,
			Base:             base,
			Quote:            quote,
			FoundAtTimestamp: now.UnixMilli(),
		}
		fillAPIDateStrings(&item, now)
		doc.Items = append(doc.Items, item)
	}
	doc.SavedAt = now.Format(time.RFC3339)
	data, _ := json.MarshalIndent(doc, "", "  ")
	atomicWrite(path, append(data, '\n'))
}

func splitMarket(m string) (string, string) {
	parts := strings.Split(strings.ToUpper(strings.TrimSpace(m)), "-")
	if len(parts) >= 2 {
		return parts[1], parts[0]
	}
	return m, ""
}

func loadMarketsCache(path string) []string {
	f, err := os.Open(path)
	if err != nil {
		return []string{}
	}
	defer f.Close()
	dec := json.NewDecoder(f)
	dec.DisallowUnknownFields()
	var c marketsCache
	if err := dec.Decode(&c); err == nil && len(c.Markets) > 0 {
		sort.Strings(c.Markets)
		return c.Markets
	}
	var arr []string
	if _, err := f.Seek(0, 0); err == nil {
		if err2 := json.NewDecoder(f).Decode(&arr); err2 == nil {
			sort.Strings(arr)
			return arr
		}
	}
	return []string{}
}

func writeMarketsCache(path string, markets []string) {
	c := marketsCache{Markets: markets, SavedAt: time.Now().UTC().Format(time.RFC3339)}
	b, _ := json.MarshalIndent(c, "", "  ")
	atomicWrite(path, append(b, '\n'))
}

func fillAPIDateStrings(item *apiPairItem, utc time.Time) {
	moscow, _ := time.LoadLocation("Europe/Moscow")
	kst, _ := time.LoadLocation("Asia/Seoul")
	item.DateUTC = utc.Format("2006.01.02 15:04")
	item.DateMSK = utc.In(moscow).Format("2006.01.02 15:04")
	item.DateKST = utc.In(kst).Format("2006.01.02 15:04")
}

func atomicWrite(path string, data []byte) {
	dir := filepath.Dir(path)
	tmp, err := os.CreateTemp(dir, filepath.Base(path)+".*.tmp")
	if err != nil {
		return
	}
	defer func() { _ = os.Remove(tmp.Name()) }()
	if _, err := tmp.Write(data); err != nil {
		_ = tmp.Close()
		return
	}
	if err := tmp.Sync(); err != nil {
		_ = tmp.Close()
		return
	}
	_ = tmp.Close()
	_ = os.Rename(tmp.Name(), path)
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// startClientCycles starts independent cycles for each HTTP client (direct + proxies)
func startClientCycles(outPath string) error {
	// Check if client cycles are already running
	if proxyCyclesStarted {
		log.Printf("client-cycles: cycles already running, skipping")
		return nil
	}

	log.Printf("client-cycles: starting independent cycles for %d HTTP clients (direct + proxies)", len(httpClients))

	// Calculate staggered start delay to distribute requests evenly
	var startDelay time.Duration
	if len(httpClients) > 1 {
		startDelay = cycleInterval / time.Duration(len(httpClients))
		log.Printf("client-cycles: using staggered start delay of %v between clients", startDelay)
	}

	// Start independent cycle for each HTTP client (including direct) with staggered delays
	for i := 0; i < len(httpClients); i++ {
		clientIndex := i
		clientLabel := httpClientLabels[i]

		go func() {
			// Apply staggered start delay (first client starts immediately)
			if clientIndex > 0 {
				initialDelay := time.Duration(clientIndex) * startDelay
				log.Printf("client-cycles: %s waiting %v before starting", clientLabel, initialDelay)
				time.Sleep(initialDelay)
			}

			log.Printf("client-cycles: starting cycle for %s", clientLabel)

			// Each client works independently with its own cycle
			ticker := time.NewTicker(cycleInterval)
			defer ticker.Stop()

			// First iteration immediately after delay
			if err := runClientIteration(outPath, clientIndex, clientLabel); err != nil {
				log.Printf("client-cycles: %s iteration error: %v", clientLabel, err)
			}

			// Then continue with ticker
			for range ticker.C {
				// Check if this client is still enabled
				proxyStatsMutex.RLock()
				stats, exists := proxyStatsMap[clientLabel]
				enabled := exists && stats.enabled
				proxyStatsMutex.RUnlock()

				if !enabled {
					log.Printf("client-cycles: %s disabled, stopping cycle", clientLabel)
					return
				}

				if err := runClientIteration(outPath, clientIndex, clientLabel); err != nil {
					log.Printf("client-cycles: %s iteration error: %v", clientLabel, err)
				}
			}
		}()
	}

	// Start statistics reporting goroutine
	go func() {
		statsTicker := time.NewTicker(30 * time.Second) // Report stats every 30 seconds
		defer statsTicker.Stop()

		for range statsTicker.C {
			printProxyStats()
		}
	}()

	// Start backup cleanup goroutine
	go func() {
		cleanupTicker := time.NewTicker(5 * time.Minute) // Cleanup backups every 5 minutes
		defer cleanupTicker.Stop()

		for range cleanupTicker.C {
			cleanupOldBackups()
		}
	}()

	// Wait for all cycles to start
	time.Sleep(100 * time.Millisecond)

	// Start forced parsing worker for announcements API if enabled
	if currentConfig != nil && currentConfig.Settings.AnnouncementsAPI.ForcedParsing.Enabled {
		startForcedParsingWorker("announcements_api", currentConfig.Settings.AnnouncementsAPI.ForcedParsing, func() {
			// Trigger additional parsing iteration for all clients
			for i := 0; i < len(httpClients); i++ {
				clientIndex := i
				clientLabel := httpClientLabels[i]
				go func() {
					if err := runClientIteration(outPath, clientIndex, clientLabel); err != nil {
						log.Printf("forced-parsing: announcements_api %s iteration error: %v", clientLabel, err)
					}
				}()
			}
		})
	}

	// Mark cycles as started
	proxyCyclesStarted = true

	log.Printf("client-cycles: all %d HTTP client cycles started successfully", len(httpClients))
	return nil
}

// startPairsDiffClientCycles starts independent cycles for each HTTP client (direct + proxies) for pairs_diff
func startPairsDiffClientCycles(apiOut, apiCache string, known *[]string) error {
	log.Printf("pairs-diff-client-cycles: starting independent cycles for %d HTTP clients (direct + proxies)", len(httpClients))

	// Calculate staggered start delay to distribute requests evenly
	var startDelay time.Duration
	if len(httpClients) > 1 {
		startDelay = pairsDiffInterval / time.Duration(len(httpClients))
		log.Printf("pairs-diff-client-cycles: using staggered start delay of %v between clients", startDelay)
	}

	// Start independent cycle for each HTTP client (including direct) with staggered delays
	for i := 0; i < len(httpClients); i++ {
		clientIndex := i
		clientLabel := httpClientLabels[i]

		go func() {
			// Apply staggered start delay (first client starts immediately)
			if clientIndex > 0 {
				initialDelay := time.Duration(clientIndex) * startDelay
				log.Printf("pairs-diff-client-cycles: %s waiting %v before starting", clientLabel, initialDelay)
				time.Sleep(initialDelay)
			}

			log.Printf("pairs-diff-client-cycles: starting cycle for %s", clientLabel)

			// Each client works independently with its own cycle
			ticker := time.NewTicker(pairsDiffInterval)
			defer ticker.Stop()

			// First iteration immediately after delay
			if err := runPairsDiffClientIteration(apiOut, apiCache, known, clientIndex, clientLabel); err != nil {
				log.Printf("pairs-diff-client-cycles: %s iteration error: %v", clientLabel, err)
			}

			// Then continue with ticker
			for range ticker.C {
				// Check if this client is still enabled
				proxyStatsMutex.RLock()
				stats, exists := proxyStatsMap[clientLabel]
				enabled := exists && stats.enabled
				proxyStatsMutex.RUnlock()

				if !enabled {
					log.Printf("pairs-diff-client-cycles: %s disabled, stopping cycle", clientLabel)
					return
				}

				if err := runPairsDiffClientIteration(apiOut, apiCache, known, clientIndex, clientLabel); err != nil {
					log.Printf("pairs-diff-client-cycles: %s iteration error: %v", clientLabel, err)
				}
			}
		}()
	}

	// Wait for all cycles to start
	time.Sleep(100 * time.Millisecond)

	// Start forced parsing worker for markets API if enabled
	if currentConfig != nil && currentConfig.Settings.MarketsAPI.ForcedParsing.Enabled {
		startForcedParsingWorker("markets_api", currentConfig.Settings.MarketsAPI.ForcedParsing, func() {
			// Trigger additional parsing iteration for all clients
			for i := 0; i < len(httpClients); i++ {
				clientIndex := i
				clientLabel := httpClientLabels[i]
				go func() {
					if err := runPairsDiffClientIteration(apiOut, apiCache, known, clientIndex, clientLabel); err != nil {
						log.Printf("forced-parsing: markets_api %s iteration error: %v", clientLabel, err)
					}
				}()
			}
		})
	}

	log.Printf("pairs-diff-client-cycles: all %d HTTP client cycles started successfully", len(httpClients))
	return nil
}

// runPairsDiffClientIteration runs a single pairs_diff iteration for a specific HTTP client
func runPairsDiffClientIteration(apiOut, apiCache string, known *[]string, clientIndex int, clientLabel string) error {
	startTime := time.Now()

	// Use only this specific client
	client := httpClients[clientIndex]

	// Fetch markets using this client
	markets, err := fetchUpbitMarkets(client)
	if err != nil {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("pairs_diff", false)
		return fmt.Errorf("pairs_diff client request failed: %w", err)
	}

	// Success! Update stats
	updateProxyStats(clientLabel, true)
	updateIntervalStatsForSource("pairs_diff", true)

	// Calculate time since last update
	proxyStatsMutex.RLock()
	stats, exists := proxyStatsMap[clientLabel]
	timeSinceLastUpdate := "N/A"
	if exists && !stats.lastUpdateTime.IsZero() {
		timeSinceLastUpdate = fmt.Sprintf("%dms", time.Since(stats.lastUpdateTime).Milliseconds())
	}
	proxyStatsMutex.RUnlock()

	log.Printf("pairs-diff-client-cycles: %s fetched %d markets in %v (last update: %s ago)",
		clientLabel, len(markets), time.Since(startTime), timeSinceLastUpdate)

	// Check for new markets
	newOnes := diffNewMarkets(*known, markets)
	if len(newOnes) > 0 {
		log.Printf("pairs-diff-client-cycles: %s detected %d new markets", clientLabel, len(newOnes))
		appendNewPairs(apiOut, markets)
		writeMarketsCache(apiCache, markets)
		// Update known markets atomically
		func() {
			*known = markets
		}()
	} else {
		log.Printf("pairs-diff-client-cycles: %s no new markets detected", clientLabel)
	}

	return nil
}

// runProxyIteration runs a single iteration for a specific proxy
func runProxyIteration(outPath string, proxyIndex int, proxyLabel string) error {
	startTime := time.Now()

	// Use only this specific proxy client
	proxyClient := httpClients[proxyIndex]

	// Fetch data using this proxy
	baseAPI := "https://api-manager.upbit.com/api/v1/announcements" // fallback
	if currentConfig != nil {
		baseAPI = currentConfig.GetAnnouncementsAPI()
	}
	url := fmt.Sprintf("%s?os=web&page=1&per_page=20&category=trade", baseAPI)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "max-age=0")
	req.Header.Set("sec-ch-ua-platform", "\"macOS\"")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	res, err := proxyClient.Do(req)
	if err != nil {
		return fmt.Errorf("proxy request failed: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("proxy status %d", res.StatusCode)
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	var out apiResponse
	if err := json.Unmarshal(body, &out); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !out.Success {
		return fmt.Errorf("API responded with success=false")
	}

	// Process new data if any
	if len(out.Data.Notices) > 0 {
		log.Printf("proxy-cycles: %s found %d notices in %v", proxyLabel, len(out.Data.Notices), time.Since(startTime))

		// Check for new listings
		existing, _ := readCache(outPath)
		existingSet := make(map[string]struct{}, len(existing))
		for _, it := range existing {
			existingSet[it.Href] = struct{}{}
		}

		var newItems []cacheItem
		for _, n := range out.Data.Notices {
			if !isTradeCategory(n.Category) {
				continue
			}

			href := getServiceCenterURL(n.ID)
			if _, exists := existingSet[href]; exists {
				continue // Already exists
			}

			parsedTime := parseKST(n.ListedAt)
			symbol := extractSymbol(n.Title)

			item := cacheItem{
				Category:         strings.TrimSpace(n.Category),
				ListedAt:         n.ListedAt,
				FirstListedAt:    n.FirstListedAt,
				ID:               n.ID,
				Href:             href,
				Symbol:           symbol,
				Title:            normalizeTitle(n.Title),
				Timestamp:        parsedTime.UnixMilli(),
				SavedAtTimestamp: time.Now().UnixMilli(),
			}

			if isListingTitle(item.Title) {
				item.Listing = true
			}

			newItems = append(newItems, item)
		}

		// Update cache with new items
		if len(newItems) > 0 {
			log.Printf("proxy-cycles: %s adding %d new items", proxyLabel, len(newItems))
			merged := append(existing, newItems...)
			if err := writeCache(outPath, merged); err != nil {
				log.Printf("proxy-cycles: %s failed to write cache: %v", proxyLabel, err)
			}

			// Spawn telegram signals for new items
			spawnTelegramSignals(newItems)

			// Handle trading if enabled
			if tradeEnabled && len(configuredAccounts) > 0 {
				if err := handleTradingWithAccounts(newItems, configuredAccounts); err != nil {
					log.Printf("proxy-cycles: %s trading error: %v", proxyLabel, err)
				}
			}
		}
	}

	return nil
}

// runClientIteration runs a single iteration for a specific HTTP client
func runClientIteration(outPath string, clientIndex int, clientLabel string) error {
	startTime := time.Now()

	// Use only this specific client
	client := httpClients[clientIndex]

	// Fetch data using this client
	baseAPI := "https://api-manager.upbit.com/api/v1/announcements" // fallback
	if currentConfig != nil {
		baseAPI = currentConfig.GetAnnouncementsAPI()
	}
	url := fmt.Sprintf("%s?os=web&page=1&per_page=20&category=trade", baseAPI)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		updateProxyStats(clientLabel, false)
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "max-age=0")
	req.Header.Set("sec-ch-ua-platform", "\"macOS\"")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	res, err := client.Do(req)
	if err != nil {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("news", false)
		return fmt.Errorf("client request failed: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("news", false)
		return fmt.Errorf("client status %d", res.StatusCode)
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("news", false)
		return fmt.Errorf("failed to read response: %w", err)
	}

	var out apiResponse
	if err := json.Unmarshal(body, &out); err != nil {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("news", false)
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !out.Success {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("news", false)
		return fmt.Errorf("API responded with success=false")
	}

	// Success! Stats will be updated after processing data

	// Process new data if any
	if len(out.Data.Notices) > 0 {
		// Calculate time since last update BEFORE updating it
		proxyStatsMutex.RLock()
		stats, exists := proxyStatsMap[clientLabel]
		timeSinceLastUpdate := "N/A"
		if exists && !stats.lastUpdateTime.IsZero() {
			timeSinceLastUpdate = fmt.Sprintf("%dms", time.Since(stats.lastUpdateTime).Milliseconds())
		}
		proxyStatsMutex.RUnlock()

		log.Printf("client-cycles: %s found %d notices in %v (last update: %s ago)",
			clientLabel, len(out.Data.Notices), time.Since(startTime), timeSinceLastUpdate)

		// Now update the lastUpdateTime in updateProxyStats
		// This will be called after processing the data
		updateProxyStats(clientLabel, true)
		updateIntervalStatsForSource("news", true)

		// Check for new listings
		existing, _ := readCache(outPath)
		existingSet := make(map[string]struct{}, len(existing))
		for _, it := range existing {
			existingSet[it.Href] = struct{}{}
		}

		var newItems []cacheItem
		for _, n := range out.Data.Notices {
			if !isTradeCategory(n.Category) {
				continue
			}

			href := getServiceCenterURL(n.ID)
			if _, exists := existingSet[href]; exists {
				continue // Already exists
			}

			parsedTime := parseKST(n.ListedAt)
			symbol := extractSymbol(n.Title)

			item := cacheItem{
				Category:         strings.TrimSpace(n.Category),
				ListedAt:         n.ListedAt,
				FirstListedAt:    n.FirstListedAt,
				ID:               n.ID,
				Href:             href,
				Symbol:           symbol,
				Title:            normalizeTitle(n.Title),
				Timestamp:        parsedTime.UnixMilli(),
				SavedAtTimestamp: time.Now().UnixMilli(),
			}

			if isListingTitle(item.Title) {
				item.Listing = true
			}

			newItems = append(newItems, item)
		}

		// Update cache with new items
		if len(newItems) > 0 {
			log.Printf("client-cycles: %s adding %d new items", clientLabel, len(newItems))
			merged := append(existing, newItems...)
			if err := writeCache(outPath, merged); err != nil {
				log.Printf("client-cycles: %s failed to write cache: %v", clientLabel, err)
			}

			// Spawn telegram signals for new items
			spawnTelegramSignals(newItems)

			// Handle trading if enabled
			if tradeEnabled && len(configuredAccounts) > 0 {
				if err := handleTradingWithAccounts(newItems, configuredAccounts); err != nil {
					log.Printf("client-cycles: %s trading error: %v", clientLabel, err)
				}
			}
		}
	}

	return nil
}

// updateProxyStats updates statistics for a specific client
func updateProxyStats(clientLabel string, success bool) {
	proxyStatsMutex.Lock()
	defer proxyStatsMutex.Unlock()

	stats, exists := proxyStatsMap[clientLabel]
	if !exists {
		return
	}

	if success {
		stats.successCount++
		stats.lastSuccess = time.Now()
		stats.consecutiveErrors = 0

		// Update lastUpdateTime when we get new data
		stats.lastUpdateTime = time.Now()

		log.Printf("proxy-stats: %s success (total: %d, errors: %d)", clientLabel, stats.successCount, stats.errorCount)
	} else {
		stats.errorCount++
		stats.lastError = time.Now()
		stats.consecutiveErrors++
		log.Printf("proxy-stats: %s error (total: %d, errors: %d, consecutive: %d)", clientLabel, stats.successCount, stats.errorCount, stats.consecutiveErrors)

		// Check if proxy should be disabled
		if shouldDisableProxy(stats) {
			disableProxy(clientLabel, stats)
		}
	}

	// Note: interval tuning stats are updated separately in source-specific functions
}

// shouldDisableProxy determines if a proxy should be disabled based on its performance
func shouldDisableProxy(stats *proxyStats) bool {
	total := stats.successCount + stats.errorCount

	// Disable after max consecutive errors
	if stats.consecutiveErrors >= maxConsecutiveErrors {
		return true
	}

	// Need minimum attempts to make decision
	if total < 3 {
		return false
	}

	// Calculate error rate
	errorRate := float64(stats.errorCount) / float64(total)

	// Disable if error rate is too high
	if errorRate > errorThreshold {
		return true
	}

	// Disable if success rate is too low
	successRate := float64(stats.successCount) / float64(total)
	if successRate < minSuccessRate {
		return true
	}

	return false
}

// disableProxy disables a proxy and logs the reason
func disableProxy(clientLabel string, stats *proxyStats) {
	if !stats.enabled {
		return // Already disabled
	}

	stats.enabled = false
	total := stats.successCount + stats.errorCount
	errorRate := float64(stats.errorCount) / float64(total)

	log.Printf("proxy-stats: DISABLING %s - consecutive errors: %d, error rate: %.1f%%, total attempts: %d",
		clientLabel, stats.consecutiveErrors, errorRate*100, total)

	// Remove proxy from config.json if it's not the direct client
	if clientLabel != "direct" {
		if err := removeProxyFromConfig(clientLabel); err != nil {
			log.Printf("proxy-stats: failed to remove %s from config: %v", clientLabel, err)
		} else {
			log.Printf("proxy-stats: successfully removed %s from config.json", clientLabel)
		}
	}
}

// removeProxyFromConfig removes a disabled proxy from config.json
func removeProxyFromConfig(proxyLabel string) error {
	// Parse proxy label to extract IP and port
	parts := strings.Split(proxyLabel, ":")
	if len(parts) != 3 {
		return fmt.Errorf("invalid proxy label format: %s", proxyLabel)
	}

	// Read current config
	configPath := "config.json"
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config: %w", err)
	}

	// Parse JSON
	var config map[string]interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Find proxies array
	proxies, ok := config["proxies"].([]interface{})
	if !ok {
		return fmt.Errorf("proxies section not found in config")
	}

	// Create backup
	backupPath := fmt.Sprintf("config.json.backup.%s", time.Now().Format("20060102-150405"))
	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		log.Printf("proxy-stats: warning: failed to create backup %s: %v", backupPath, err)
	} else {
		log.Printf("proxy-stats: created backup: %s", backupPath)
	}

	// Find and remove the proxy
	var newProxies []interface{}
	removed := false

	for _, proxy := range proxies {
		proxyStr, ok := proxy.(string)
		if !ok {
			continue
		}

		// Check if this proxy matches the disabled one
		if strings.Contains(proxyStr, parts[1]+":"+parts[2]) { // IP:port
			log.Printf("proxy-stats: removing proxy from config: %s", proxyStr)
			removed = true
			continue
		}

		newProxies = append(newProxies, proxy)
	}

	if !removed {
		log.Printf("proxy-stats: warning: proxy %s not found in config", proxyLabel)
		return nil
	}

	// Update config
	config["proxies"] = newProxies

	// Write updated config
	newData, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal updated config: %w", err)
	}

	// Write to temporary file first
	tmpPath := configPath + ".tmp"
	if err := os.WriteFile(tmpPath, newData, 0644); err != nil {
		return fmt.Errorf("failed to write temp config: %w", err)
	}

	// Atomic rename
	if err := os.Rename(tmpPath, configPath); err != nil {
		// Clean up temp file
		_ = os.Remove(tmpPath)
		return fmt.Errorf("failed to rename temp config: %w", err)
	}

	log.Printf("proxy-stats: config.json updated, removed %s", proxyLabel)
	return nil
}

// cleanupOldBackups removes old backup files, keeping only the last 5
func cleanupOldBackups() {
	pattern := "config.json.backup.*"
	matches, err := filepath.Glob(pattern)
	if err != nil {
		log.Printf("proxy-stats: failed to find backup files: %v", err)
		return
	}

	if len(matches) <= 5 {
		return // Keep all if 5 or fewer
	}

	// Sort by modification time (oldest first)
	type backupFile struct {
		path    string
		modTime time.Time
	}

	var backups []backupFile
	for _, match := range matches {
		info, err := os.Stat(match)
		if err != nil {
			continue
		}
		backups = append(backups, backupFile{match, info.ModTime()})
	}

	sort.Slice(backups, func(i, j int) bool {
		return backups[i].modTime.Before(backups[j].modTime)
	})

	// Remove oldest backups, keep last 5
	toRemove := len(backups) - 5
	for i := 0; i < toRemove; i++ {
		if err := os.Remove(backups[i].path); err != nil {
			log.Printf("proxy-stats: failed to remove old backup %s: %v", backups[i].path, err)
		} else {
			log.Printf("proxy-stats: removed old backup: %s", backups[i].path)
		}
	}
}

// getActiveClients returns only enabled clients
func getActiveClients() ([]*http.Client, []string) {
	proxyStatsMutex.RLock()
	defer proxyStatsMutex.RUnlock()

	var activeClients []*http.Client
	var activeLabels []string

	for i, label := range httpClientLabels {
		if stats, exists := proxyStatsMap[label]; exists && stats.enabled {
			activeClients = append(activeClients, httpClients[i])
			activeLabels = append(activeLabels, label)
		}
	}

	return activeClients, activeLabels
}

// printProxyStats prints current statistics for all proxies
func printProxyStats() {
	proxyStatsMutex.RLock()
	defer proxyStatsMutex.RUnlock()

	log.Printf("proxy-stats: === PROXY STATISTICS ===")
	for label, stats := range proxyStatsMap {
		total := stats.successCount + stats.errorCount
		if total == 0 {
			continue
		}

		successRate := float64(stats.successCount) / float64(total) * 100
		errorRate := float64(stats.errorCount) / float64(total) * 100
		status := "ENABLED"
		if !stats.enabled {
			status = "DISABLED"
		}

		// Calculate time since last update
		timeSinceUpdate := time.Since(stats.lastUpdateTime)
		updateInterval := "N/A"
		if !stats.lastUpdateTime.IsZero() {
			updateInterval = fmt.Sprintf("%dms", timeSinceUpdate.Milliseconds())
		}

		log.Printf("proxy-stats: %s [%s] - Success: %d (%.1f%%), Errors: %d (%.1f%%), Consecutive: %d, Last Update: %s ago",
			label, status, stats.successCount, successRate, stats.errorCount, errorRate, stats.consecutiveErrors, updateInterval)
	}
	log.Printf("proxy-stats: === END STATISTICS ===")
}

// initIntervalStats initializes interval tuning statistics
func initIntervalStats(cfg *Config) {
	intervalStatsMutex.Lock()
	defer intervalStatsMutex.Unlock()

	// Load tuning configuration
	intervalTuningEnabled = cfg.Settings.IntervalTuning.Enabled
	intervalTuneStep = cfg.Settings.IntervalTuning.TuneStepMs
	intervalCheckPeriod = time.Duration(cfg.Settings.IntervalTuning.CheckPeriodMinutes) * time.Minute
	maxRollbackCount = cfg.Settings.IntervalTuning.MaxRollbackCount
	minInterval = cfg.Settings.IntervalTuning.MinIntervalMs
	maxInterval = cfg.Settings.IntervalTuning.MaxIntervalMs

	// Disable interval tuning in dev mode (no rate limits on localhost)
	if cfg.Settings.API.Playground == "dev" {
		intervalTuningEnabled = false
		log.Printf("interval-tuning: disabled in dev mode (no rate limits on localhost)")
		return
	}

	if !intervalTuningEnabled {
		log.Printf("interval-tuning: disabled in configuration")
		return
	}

	now := time.Now()

	// Initialize announcements API interval stats
	newsIntervalStats = &intervalStats{
		startTime:        now,
		originalInterval: cfg.Settings.AnnouncementsAPI.CycleIntervalMs,
		currentInterval:  cfg.Settings.AnnouncementsAPI.CycleIntervalMs,
		isFixed:          cfg.Settings.AnnouncementsAPI.IntervalFixed,
		lastTuneTime:     now,
	}

	// Initialize markets API interval stats
	pairsDiffIntervalStats = &intervalStats{
		startTime:        now,
		originalInterval: cfg.Settings.MarketsAPI.IntervalMs,
		currentInterval:  cfg.Settings.MarketsAPI.IntervalMs,
		isFixed:          cfg.Settings.MarketsAPI.IntervalFixed,
		lastTuneTime:     now,
	}

	// Initialize service center interval stats
	serviceCenterIntervalStats = &intervalStats{
		startTime:        now,
		originalInterval: cfg.Settings.ServiceCenter.IntervalMs,
		currentInterval:  cfg.Settings.ServiceCenter.IntervalMs,
		isFixed:          cfg.Settings.ServiceCenter.IntervalFixed,
		lastTuneTime:     now,
	}

	log.Printf("interval-tuning: initialized - news: %dms (fixed: %v), pairs_diff: %dms (fixed: %v), service_center: %dms (fixed: %v)",
		newsIntervalStats.currentInterval, newsIntervalStats.isFixed,
		pairsDiffIntervalStats.currentInterval, pairsDiffIntervalStats.isFixed,
		serviceCenterIntervalStats.currentInterval, serviceCenterIntervalStats.isFixed)
	log.Printf("interval-tuning: config - step: %dms, period: %v, max_rollbacks: %d, range: %d-%dms",
		intervalTuneStep, intervalCheckPeriod, maxRollbackCount, minInterval, maxInterval)

	// Start interval tuning goroutine if enabled and not all fixed
	if !newsIntervalStats.isFixed || !pairsDiffIntervalStats.isFixed || !serviceCenterIntervalStats.isFixed {
		go intervalTuningWorker()
	}
}

// intervalTuningWorker monitors and adjusts intervals based on error rates
func intervalTuningWorker() {
	ticker := time.NewTicker(intervalCheckPeriod)
	defer ticker.Stop()

	log.Printf("interval-tuning: worker started, checking every %v", intervalCheckPeriod)

	for range ticker.C {
		intervalStatsMutex.Lock()

		// Check news interval
		if !newsIntervalStats.isFixed {
			checkAndAdjustInterval("news", newsIntervalStats)
		}

		// Check pairs_diff interval
		if !pairsDiffIntervalStats.isFixed {
			checkAndAdjustInterval("pairs_diff", pairsDiffIntervalStats)
		}

		intervalStatsMutex.Unlock()
	}
}

// checkAndAdjustInterval checks if interval should be adjusted based on error rate
func checkAndAdjustInterval(source string, stats *intervalStats) {
	if stats.isFixed {
		return
	}

	total := stats.successCount + stats.errorCount
	if total == 0 {
		log.Printf("interval-tuning: %s - no requests in the last hour, skipping adjustment", source)
		return
	}

	errorRate := float64(stats.errorCount) / float64(total)
	log.Printf("interval-tuning: %s - hour stats: success=%d, errors=%d, rate=%.1f%%, interval=%dms",
		source, stats.successCount, stats.errorCount, errorRate*100, stats.currentInterval)

	if stats.errorCount > 0 {
		// Errors detected - increase interval (rollback)
		newInterval := stats.currentInterval + intervalTuneStep
		if newInterval > maxInterval {
			newInterval = maxInterval
		}

		stats.rollbackCount++
		log.Printf("interval-tuning: %s - errors detected, increasing interval %dms -> %dms (rollback #%d)",
			source, stats.currentInterval, newInterval, stats.rollbackCount)

		updateInterval(source, newInterval)

		// Check if we should fix the interval
		if stats.rollbackCount >= maxRollbackCount {
			stats.isFixed = true
			log.Printf("interval-tuning: %s - reached max rollbacks (%d), fixing interval at %dms",
				source, maxRollbackCount, newInterval)
			updateConfigIntervalFixed(source, true)
		}
	} else {
		// No errors - try to decrease interval (optimize)
		newInterval := stats.currentInterval - intervalTuneStep
		if newInterval < minInterval {
			newInterval = minInterval
		}

		if newInterval != stats.currentInterval {
			log.Printf("interval-tuning: %s - no errors, decreasing interval %dms -> %dms",
				source, stats.currentInterval, newInterval)
			updateInterval(source, newInterval)
		} else {
			log.Printf("interval-tuning: %s - already at minimum interval (%dms)", source, minInterval)
		}
	}

	// Reset counters for next hour
	stats.successCount = 0
	stats.errorCount = 0
	stats.startTime = time.Now()
	stats.lastTuneTime = time.Now()
}

// updateInterval updates the runtime interval and saves to config
func updateInterval(source string, newInterval int) {
	// Update runtime variables
	if source == "news" {
		cycleInterval = time.Duration(newInterval) * time.Millisecond
		newsIntervalStats.currentInterval = newInterval
		if loadedConfig != nil {
			loadedConfig.Settings.AnnouncementsAPI.CycleIntervalMs = newInterval
		}
	} else if source == "pairs_diff" {
		pairsDiffInterval = time.Duration(newInterval) * time.Millisecond
		pairsDiffIntervalStats.currentInterval = newInterval
		if loadedConfig != nil {
			loadedConfig.Settings.MarketsAPI.IntervalMs = newInterval
		}
	} else if source == "service_center" {
		serviceCenterIntervalStats.currentInterval = newInterval
		if loadedConfig != nil {
			loadedConfig.Settings.ServiceCenter.IntervalMs = newInterval
		}
	}

	// Save to config file
	if loadedConfig != nil {
		if err := SaveConfigIntervals(loadedConfig, ""); err != nil {
			log.Printf("interval-tuning: failed to save config: %v", err)
		} else {
			log.Printf("interval-tuning: %s interval updated to %dms and saved to config.json", source, newInterval)
		}
	}
}

// updateConfigIntervalFixed updates the interval_fixed flag in config
func updateConfigIntervalFixed(source string, fixed bool) {
	if loadedConfig == nil {
		return
	}

	if source == "news" {
		loadedConfig.Settings.AnnouncementsAPI.IntervalFixed = fixed
	} else if source == "pairs_diff" {
		loadedConfig.Settings.MarketsAPI.IntervalFixed = fixed
	}

	if err := SaveConfigIntervals(loadedConfig, ""); err != nil {
		log.Printf("interval-tuning: failed to save interval_fixed flag: %v", err)
	} else {
		log.Printf("interval-tuning: %s interval_fixed set to %v and saved to config.json", source, fixed)
	}
}

// updateIntervalStatsForSource updates interval tuning statistics for specific source
func updateIntervalStatsForSource(source string, success bool) {
	intervalStatsMutex.Lock()
	defer intervalStatsMutex.Unlock()

	if source == "news" && newsIntervalStats != nil && !newsIntervalStats.isFixed {
		if success {
			newsIntervalStats.successCount++
		} else {
			newsIntervalStats.errorCount++
		}
	} else if source == "pairs_diff" && pairsDiffIntervalStats != nil && !pairsDiffIntervalStats.isFixed {
		if success {
			pairsDiffIntervalStats.successCount++
		} else {
			pairsDiffIntervalStats.errorCount++
		}
	} else if source == "service_center" && serviceCenterIntervalStats != nil && !serviceCenterIntervalStats.isFixed {
		if success {
			serviceCenterIntervalStats.successCount++
		} else {
			serviceCenterIntervalStats.errorCount++
		}
	}
}

// testTrading runs a test of the trading logic with a mock listing
func testTrading() {
	fmt.Println("# test-trading")
	fmt.Println("Testing trading logic with mock listing...")

	// Load .env and config
	loadDotEnv()
	cfg, err := LoadConfig("")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	loadedConfig = cfg
	currentConfig = cfg
	configuredAccounts = cfg.Accounts

	// Enable test mode
	os.Setenv("UPBIT_TEST_TRADING", "1")

	// Create mock listing item
	now := time.Now().UnixMilli()
	mockItem := cacheItem{
		Category:               "trade",
		ListedAt:               time.Now().Format(time.RFC3339),
		FirstListedAt:          time.Now().Format(time.RFC3339),
		ID:                     999999,
		Href:                   getServiceCenterURL(999999),
		Symbol:                 "API3",
		Title:                  "Market Support for API3 (API3) Trading",
		Listing:                true,
		Timestamp:              now,
		SavedAtTimestamp:       now,
		ListedAtTimestamp:      now,
		FirstListedAtTimestamp: now,
	}

	fmt.Printf("Testing with mock listing: %s\n", mockItem.Symbol)

	// Test trading
	if err := handleTradingWithAccounts([]cacheItem{mockItem}, configuredAccounts); err != nil {
		log.Fatalf("Trading test failed: %v", err)
	}

	fmt.Println("Trading test completed successfully!")

	// Print test events
	fmt.Println("\nTest events recorded:")
	testEventsMu.Lock()
	for i, event := range testTradeEvents {
		fmt.Printf("%d: %s\n", i+1, event)
	}
	testEventsMu.Unlock()
}

// ========================= Service Center source =========================

// startServiceCenterClientCycles starts independent cycles for each HTTP client (direct + proxies) for service_center
func startServiceCenterClientCycles(outPath string) error {
	log.Printf("service-center-client-cycles: starting independent cycles for %d HTTP clients (direct + proxies)", len(httpClients))

	// Calculate staggered start delay to distribute requests evenly
	var startDelay time.Duration
	serviceCenterInterval := time.Duration(currentConfig.Settings.ServiceCenter.IntervalMs) * time.Millisecond
	if len(httpClients) > 1 {
		startDelay = serviceCenterInterval / time.Duration(len(httpClients))
		log.Printf("service-center-client-cycles: using staggered start delay of %v between clients", startDelay)
	}

	// Start a goroutine for each HTTP client
	for clientIndex, _ := range httpClients {
		clientLabel := httpClientLabels[clientIndex]

		go func() {
			// Apply staggered start delay (first client starts immediately)
			if clientIndex > 0 {
				initialDelay := time.Duration(clientIndex) * startDelay
				log.Printf("service-center-client-cycles: %s waiting %v before starting", clientLabel, initialDelay)
				time.Sleep(initialDelay)
			}

			log.Printf("service-center-client-cycles: starting cycle for %s", clientLabel)

			// Each client works independently with its own cycle
			ticker := time.NewTicker(serviceCenterInterval)
			defer ticker.Stop()

			// First iteration immediately after delay
			if err := runServiceCenterIteration(outPath, clientIndex, clientLabel); err != nil {
				log.Printf("service-center-client-cycles: %s iteration error: %v", clientLabel, err)
			}

			// Continue with regular intervals
			for range ticker.C {
				if err := runServiceCenterIteration(outPath, clientIndex, clientLabel); err != nil {
					log.Printf("service-center-client-cycles: %s iteration error: %v", clientLabel, err)
				}
			}
		}()
	}

	// Start forced parsing worker for service center if enabled
	if currentConfig != nil && currentConfig.Settings.ServiceCenter.ForcedParsing.Enabled {
		startForcedParsingWorker("service_center", currentConfig.Settings.ServiceCenter.ForcedParsing, func() {
			// Trigger additional parsing iteration for all clients
			for i := 0; i < len(httpClients); i++ {
				clientIndex := i
				clientLabel := httpClientLabels[i]
				go func() {
					if err := runServiceCenterIteration(outPath, clientIndex, clientLabel); err != nil {
						log.Printf("forced-parsing: service_center %s iteration error: %v", clientLabel, err)
					}
				}()
			}
		})
	}

	log.Printf("service-center-client-cycles: all %d HTTP client cycles started successfully", len(httpClients))
	return nil
}

// runServiceCenterIteration runs a single iteration for service center parsing
func runServiceCenterIteration(outPath string, clientIndex int, clientLabel string) error {
	startTime := time.Now()

	// Use only this specific client
	client := httpClients[clientIndex]

	// Get service center URL
	serviceCenterURL := "https://upbit.com/service_center/notice" // fallback
	if currentConfig != nil {
		serviceCenterURL = currentConfig.GetServiceCenterURL()
	}

	// Create request
	req, err := http.NewRequest(http.MethodGet, serviceCenterURL, nil)
	if err != nil {
		updateProxyStats(clientLabel, false)
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers to mimic browser
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// Add context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(currentConfig.Settings.ServiceCenter.TimeoutSec)*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		updateProxyStats(clientLabel, false)
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	duration := time.Since(startTime)

	if resp.StatusCode != http.StatusOK {
		updateProxyStats(clientLabel, false)
		return fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		updateProxyStats(clientLabel, false)
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Parse HTML and extract listings
	listings, err := parseServiceCenterHTML(string(body))
	if err != nil {
		updateProxyStats(clientLabel, false)
		updateIntervalStatsForSource("service_center", false)
		return fmt.Errorf("failed to parse HTML: %w", err)
	}

	// Update proxy stats
	updateProxyStats(clientLabel, true)
	updateIntervalStatsForSource("service_center", true)

	// Process found listings
	if len(listings) > 0 {
		log.Printf("service-center-client-cycles: %s found %d listings in %v (last update: %dms ago)",
			clientLabel, len(listings), duration, duration.Milliseconds())

		// Save listings to cache
		if err := saveServiceCenterListings(outPath, listings); err != nil {
			return fmt.Errorf("failed to save listings: %w", err)
		}
	} else {
		log.Printf("service-center-client-cycles: %s no new listings found in %v", clientLabel, duration)
	}

	return nil
}

// ServiceCenterListing represents a parsed listing from service center
type ServiceCenterListing struct {
	Title  string `json:"title"`
	Symbol string `json:"symbol"`
	Date   string `json:"date"`
	Href   string `json:"href"`
}

// parseServiceCenterHTML parses HTML content and extracts listings using Go equivalent of the JS parser
func parseServiceCenterHTML(htmlContent string) ([]ServiceCenterListing, error) {
	// Parse HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}

	var items []ServiceCenterListing

	// Find all anchor tags (equivalent to document.querySelectorAll("a"))
	doc.Find("a").Each(func(i int, anchor *goquery.Selection) {
		// Get text content and normalize it
		titleRaw := normalize(anchor.Text())
		if titleRaw == "" {
			return
		}

		// Apply text processing (equivalent to JS functions)
		title := collapseSplitLetters(collapseTickerInsideParens(titleRaw))

		// Check if title matches the pattern
		if !phraseRegex.MatchString(title) {
			return
		}

		// Extract symbol from parentheses
		matches := symbolRegex.FindStringSubmatch(title)
		if len(matches) < 2 {
			return
		}
		symbol := strings.ToUpper(matches[1])

		// Find date - search in parent elements and siblings
		date := findDateInContext(anchor, title)

		// Get href attribute
		href, exists := anchor.Attr("href")
		if !exists {
			href = ""
		}

		// Convert relative URLs to absolute
		if href != "" && !strings.HasPrefix(href, "http") {
			if strings.HasPrefix(href, "/") {
				href = "https://upbit.com" + href
			} else {
				href = "https://upbit.com/" + href
			}
		}

		items = append(items, ServiceCenterListing{
			Title:  title,
			Symbol: symbol,
			Date:   date,
			Href:   href,
		})
	})

	// Filter and deduplicate results
	filtered := filterAndDeduplicateListings(items)

	return filtered, nil
}

// saveServiceCenterListings saves service center listings to the cache file
func saveServiceCenterListings(outPath string, listings []ServiceCenterListing) error {
	if len(listings) == 0 {
		return nil
	}

	// Read existing cache
	existing, existingSet := readCache(outPath)

	// Convert service center listings to cache items
	var newItems []cacheItem
	for _, listing := range listings {
		// Check if already exists
		if _, exists := existingSet[listing.Href]; exists {
			continue
		}

		// Parse date
		var timestamp int64
		if listing.Date != "" {
			// Try to parse the date - this is a simplified version
			// In a real implementation, you would handle various date formats
			if t, err := time.Parse("2006.01.02", listing.Date); err == nil {
				timestamp = t.UnixMilli()
			} else {
				timestamp = time.Now().UnixMilli()
			}
		} else {
			timestamp = time.Now().UnixMilli()
		}

		item := cacheItem{
			Category:         "trade",
			ListedAt:         time.Unix(timestamp/1000, 0).Format(time.RFC3339),
			FirstListedAt:    time.Unix(timestamp/1000, 0).Format(time.RFC3339),
			ID:               0, // Service center doesn't have numeric IDs
			Href:             listing.Href,
			Symbol:           listing.Symbol,
			Title:            listing.Title,
			Listing:          true, // All service center listings are considered listings
			Timestamp:        timestamp,
			SavedAtTimestamp: time.Now().UnixMilli(),
		}

		newItems = append(newItems, item)
	}

	if len(newItems) > 0 {
		// Append new items to existing cache
		allItems := append(existing, newItems...)

		// Write back to cache
		if err := writeCache(outPath, allItems); err != nil {
			return fmt.Errorf("failed to write cache: %w", err)
		}

		log.Printf("service_center: saved %d new listings to cache", len(newItems))
	}

	return nil
}

// ========================= Service Center HTML parsing helpers =========================

// normalize removes zero-width spaces, non-breaking spaces, and normalizes whitespace
func normalize(s string) string {
	// Remove zero-width space (\u200b) and non-breaking space (\xa0)
	s = strings.ReplaceAll(s, "\u200b", "")
	s = strings.ReplaceAll(s, "\xa0", " ")

	// Replace multiple whitespace with single space and trim
	re := regexp.MustCompile(`\s+`)
	s = re.ReplaceAllString(s, " ")
	return strings.TrimSpace(s)
}

// collapseTickerInsideParens collapses spaces inside parentheses for tickers
func collapseTickerInsideParens(s string) string {
	re := regexp.MustCompile(`\(\s*([A-Za-z0-9\-\s]{1,60})\s*\)`)
	return re.ReplaceAllStringFunc(s, func(match string) string {
		// Extract content inside parentheses
		inner := re.FindStringSubmatch(match)[1]
		// Remove all spaces from inner content
		inner = strings.ReplaceAll(inner, " ", "")
		return "(" + inner + ")"
	})
}

// collapseSplitLetters joins single letters that are separated by spaces
func collapseSplitLetters(s string) string {
	tokens := strings.Fields(s)
	var result []string
	var buffer []string

	for _, token := range tokens {
		if len(token) == 1 && isLetter(token) {
			buffer = append(buffer, token)
		} else {
			if len(buffer) > 0 {
				result = append(result, strings.Join(buffer, ""))
				buffer = nil
			}
			result = append(result, token)
		}
	}

	// Handle remaining buffer
	if len(buffer) > 0 {
		result = append(result, strings.Join(buffer, ""))
	}

	// Join and clean up spacing around punctuation
	s2 := strings.Join(result, " ")
	s2 = regexp.MustCompile(`\s*\(\s*`).ReplaceAllString(s2, " (")
	s2 = regexp.MustCompile(`\s*\)\s*`).ReplaceAllString(s2, ") ")
	s2 = regexp.MustCompile(`\s*,\s*`).ReplaceAllString(s2, ", ")

	// Normalize whitespace again
	s2 = regexp.MustCompile(`\s+`).ReplaceAllString(s2, " ")
	return strings.TrimSpace(s2)
}

// isLetter checks if a string contains only letters
func isLetter(s string) bool {
	for _, r := range s {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return true
}

// findDateInContext searches for dates in the context around an anchor element
func findDateInContext(anchor *goquery.Selection, title string) string {
	// Search in parent elements (up to 6 levels)
	current := anchor
	for hop := 0; hop < 6 && current != nil; hop++ {
		current = current.Parent()
		if current.Length() == 0 {
			break
		}

		parentText := normalize(current.Text())
		if date := closestDateInText(parentText, title); date != "" {
			return date
		}

		// Check previous and next siblings
		if prev := current.Prev(); prev.Length() > 0 {
			if date := findFirstDate(normalize(prev.Text())); date != "" {
				return date
			}
		}

		if next := current.Next(); next.Length() > 0 {
			if date := findFirstDate(normalize(next.Text())); date != "" {
				return date
			}
		}
	}

	// If no date found in context, search in the entire document
	doc := anchor.Parents().Last()
	pageText := normalize(doc.Text())

	// Get anchor key for position-based search
	anchorKey := ""
	if parts := strings.Split(title, " ("); len(parts) > 0 {
		anchorKey = parts[0]
		if len(anchorKey) > 40 {
			anchorKey = anchorKey[:40]
		}
	}

	if anchorKey != "" {
		pos := strings.Index(pageText, anchorKey)
		if pos >= 0 {
			// Extract window around the anchor position
			start := pos - 2000
			if start < 0 {
				start = 0
			}
			end := pos + 2000
			if end > len(pageText) {
				end = len(pageText)
			}
			window := pageText[start:end]

			if date := closestDateInText(window, title); date != "" {
				return date
			}
			if date := findFirstDate(window); date != "" {
				return date
			}
		}
	}

	// Last resort: find any date in the page
	return findFirstDate(pageText)
}

// findFirstDate finds the first date in text using various patterns
func findFirstDate(text string) string {
	for _, regex := range dateRegexes {
		if match := regex.FindString(text); match != "" {
			return match
		}
	}
	return ""
}

// closestDateInText finds the date closest to the anchor text
func closestDateInText(text, anchor string) string {
	if text == "" {
		return ""
	}

	// Get anchor key for position calculation
	anchorKey := ""
	if parts := strings.Split(anchor, " ("); len(parts) > 0 {
		anchorKey = parts[0]
		if len(anchorKey) > 40 {
			anchorKey = anchorKey[:40]
		}
	}

	anchorPos := -1
	if anchorKey != "" {
		anchorPos = strings.Index(text, anchorKey)
	}

	bestDate := ""
	bestDistance := int(^uint(0) >> 1) // Max int

	for _, regex := range dateRegexes {
		matches := regex.FindAllStringIndex(text, -1)
		for _, match := range matches {
			pos := match[0]
			distance := pos
			if anchorPos >= 0 {
				distance = abs(pos - anchorPos)
			}

			if distance < bestDistance {
				bestDate = text[match[0]:match[1]]
				bestDistance = distance
			}
		}
	}

	return bestDate
}

// abs returns the absolute value of an integer
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// filterAndDeduplicateListings filters and removes duplicate listings
func filterAndDeduplicateListings(items []ServiceCenterListing) []ServiceCenterListing {
	// Filter items that match "Market Support for" pattern
	var filtered []ServiceCenterListing
	marketSupportRegex := regexp.MustCompile(`(?i)Market\s*Support\s*for`)

	for _, item := range items {
		if marketSupportRegex.MatchString(item.Title) {
			filtered = append(filtered, item)
		}
	}

	// Deduplicate based on symbol, date, and href
	seen := make(map[string]bool)
	var unique []ServiceCenterListing

	for _, item := range filtered {
		key := item.Symbol + "::" + item.Date + "::" + item.Href
		if !seen[key] {
			seen[key] = true
			unique = append(unique, item)
		}
	}

	return unique
}

// Forced parsing functionality

// getNextTargetTime returns the next target time (when minutes are divisible by 10)
func getNextTargetTime(now time.Time) time.Time {
	minute := now.Minute()
	targetMinute := ((minute / 10) + 1) * 10
	if targetMinute >= 60 {
		// Next hour
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour()+1, 0, 0, 0, now.Location())
	}
	return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), targetMinute, 0, 0, now.Location())
}

// getCurrentTargetTime returns the current target time if we're at a target minute, otherwise returns zero time
func getCurrentTargetTime(now time.Time) time.Time {
	minute := now.Minute()
	if minute%10 == 0 {
		return time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), minute, 0, 0, now.Location())
	}
	return time.Time{}
}

// isWithinForcedParsingWindow checks if current time is within the forced parsing window
func isWithinForcedParsingWindow(now time.Time, beforeMs, afterMs int) bool {
	// Check current target time
	currentTarget := getCurrentTargetTime(now)
	if !currentTarget.IsZero() {
		// We're at a target minute, check if we're within the window
		diffMs := int(now.Sub(currentTarget).Milliseconds())
		return diffMs >= 0 && diffMs <= afterMs
	}

	// Check if we're before the next target time
	nextTarget := getNextTargetTime(now)
	diffMs := int(nextTarget.Sub(now).Milliseconds())
	return diffMs <= beforeMs
}

// startForcedParsingWorker starts a worker that monitors time and triggers forced parsing
func startForcedParsingWorker(source string, config ForcedParsingConfig, triggerFunc func()) {
	if !config.Enabled {
		return
	}

	go func() {
		log.Printf("forced-parsing: %s worker started (before=%dms, after=%dms, interval=%dms)",
			source, config.BeforeMs, config.AfterMs, config.RequestIntervalMs)

		ticker := time.NewTicker(1 * time.Second) // Check every second
		defer ticker.Stop()

		var forcedParsingActive bool
		var forcedTicker *time.Ticker

		for range ticker.C {
			now := time.Now()

			// Check if we should be in forced parsing mode
			shouldBeActive := isWithinForcedParsingWindow(now, config.BeforeMs, config.AfterMs)

			// Start forced parsing if needed
			if !forcedParsingActive && shouldBeActive {
				log.Printf("forced-parsing: %s starting forced parsing at %s", source, now.Format("15:04:05"))
				forcedParsingActive = true

				// Start intensive parsing
				forcedTicker = time.NewTicker(time.Duration(config.RequestIntervalMs) * time.Millisecond)
				go func() {
					defer forcedTicker.Stop()
					for range forcedTicker.C {
						if !forcedParsingActive {
							return
						}
						triggerFunc()
					}
				}()
			}

			// Stop forced parsing if needed
			if forcedParsingActive && !shouldBeActive {
				log.Printf("forced-parsing: %s stopping forced parsing at %s", source, now.Format("15:04:05"))
				forcedParsingActive = false
				if forcedTicker != nil {
					forcedTicker.Stop()
				}
			}
		}
	}()
}
